-- GameChecker.server.lua
-- Script que verifica se tudo foi criado corretamente e exibe relatório

local Workspace = game:GetService("Workspace")
local StarterPack = game:GetService("StarterPack")
local StarterPlayer = game:GetService("StarterPlayer")
local ServerStorage = game:GetService("ServerStorage")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda um tempo para que tudo seja criado
wait(10)

print("🔍 === VERIFICAÇÃO DO JOGO === 🔍")

-- Verifica Workspace
print("\n📍 WORKSPACE:")
local baseplate = Workspace:FindFirstChild("Baseplate")
local centralSpawn = Workspace:FindFirstChild("CentralSpawn")
local bases = 0
local resources = 0

for _, obj in ipairs(Workspace:GetChildren()) do
    if obj.Name:match("Base_") then
        bases = bases + 1
    elseif obj.Name:match("Resource") then
        resources = resources + 1
    end
end

print("   ��� Baseplate: " .. (baseplate and "✅ Encontrado" or "❌ Não encontrado"))
print("   • CentralSpawn: " .. (centralSpawn and "✅ Encontrado" or "❌ Não encontrado"))
print("   • Bases criadas: " .. bases .. "/12")
print("   • Recursos criados: " .. resources .. "/50")

-- Verifica StarterPack
print("\n🎒 STARTERPACK:")
local combatGun = StarterPack:FindFirstChild("CombatGun")
local collectorGun = StarterPack:FindFirstChild("CollectorGun")

print("   • CombatGun: " .. (combatGun and "✅ Encontrado" or "❌ Não encontrado"))
print("   • CollectorGun: " .. (collectorGun and "✅ Encontrado" or "❌ Não encontrado"))

-- Verifica StarterPlayer
print("\n👤 STARTERPLAYER:")
print("   • WalkSpeed: " .. StarterPlayer.CharacterWalkSpeed)
print("   • Configurações básicas aplicadas")

-- Verifica ServerStorage
print("\n🗄️ SERVERSTORAGE:")
local baseTemplate = ServerStorage:FindFirstChild("BaseTemplate")
print("   • BaseTemplate: " .. (baseTemplate and "✅ Encontrado" or "❌ Não encontrado"))

-- Verifica ReplicatedStorage
print("\n🔄 REPLICATEDSTORAGE:")
local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
local gameConfig = ReplicatedStorage:FindFirstChild("GameConfig")

print("   • RemoteEvents: " .. (remoteEvents and "✅ Encontrado" or "❌ Não encontrado"))
print("   • GameConfig: " .. (gameConfig and "✅ Encontrado" or "❌ Não encontrado"))

if remoteEvents then
    local eventCount = #remoteEvents:GetChildren()
    print("   • Eventos criados: " .. eventCount)
end

-- Verifica StarterGui
print("\n🖥️ STARTERGUI:")
local starterGui = game:GetService("StarterGui")
local uis = {"InviteUI", "BaseInfoUI", "GameplayUI", "BuildingUI", "MainHUD"}
for _, uiName in ipairs(uis) do
    local ui = starterGui:FindFirstChild(uiName)
    print("   • " .. uiName .. ": " .. (ui and "✅ Encontrado" or "❌ Não encontrado"))
end

-- Resumo final
print("\n📊 RESUMO:")
local totalIssues = 0

if not baseplate then totalIssues = totalIssues + 1 end
if not centralSpawn then totalIssues = totalIssues + 1 end
if bases < 12 then totalIssues = totalIssues + 1 end
if resources < 50 then totalIssues = totalIssues + 1 end
if not combatGun then totalIssues = totalIssues + 1 end
if not collectorGun then totalIssues = totalIssues + 1 end
if not baseTemplate then totalIssues = totalIssues + 1 end
if not remoteEvents then totalIssues = totalIssues + 1 end

if totalIssues == 0 then
    print("🎉 TUDO FUNCIONANDO PERFEITAMENTE!")
    print("🎮 O jogo está pronto para ser jogado!")
else
    print("⚠️ " .. totalIssues .. " problemas encontrados")
    print("💡 Execute o AutoInit novamente se necessário")
end

print("\n🔧 COMANDOS ÚTEIS:")
print("   • Para reinicializar: Execute AutoInit no ServerScriptService")
print("   • Para verificar novamente: Execute este GameChecker")
print("   • Para limpar tudo: Delete objetos no Workspace e execute AutoInit")

print("\n=== FIM DA VERIFICAÇÃO ===")

-- Cria um comando para reinicializar se necessário
if totalIssues > 0 then
    print("\n🔄 Tentando reinicializar automaticamente em 5 segundos...")
    wait(5)
    
    -- Executa o AutoInit novamente
    local autoInit = script.Parent:FindFirstChild("AutoInit")
    if autoInit then
        print("🔄 Executando AutoInit novamente...")
        -- Não podemos executar diretamente, mas podemos avisar
        warn("Execute manualmente o script AutoInit para corrigir os problemas!")
    end
end