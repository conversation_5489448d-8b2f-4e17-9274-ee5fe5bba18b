-- GameConfig.lua
-- Configurações globais do jogo que podem ser acessadas tanto pelo servidor quanto pelo cliente

local GameConfig = {}

-- Configurações das Bases
GameConfig.BASE_CONFIG = {
    MAX_BASES = 12,
    BASE_SIZE_DEFAULT = 100,
    BARRIER_RADIUS = 20,
    CLAIM_PAD_SIZE = Vector3.new(8, 1, 8),
    CORE_TOWER_HEIGHT = 20,
    BASE_PLATFORM_SIZE = 30
}

-- Configurações de Equipes
GameConfig.TEAM_CONFIG = {
    MAX_PLAYERS_PER_BASE = 2,
    AVAILABLE_COLORS = {
        "Bright red",
        "Bright blue", 
        "Lime green",
        "New Yeller",
        "Bright violet",
        "Bright orange",
        "Hot pink",
        "Cyan",
        "Really black",
        "White",
        "Brown",
        "Dark green"
    }
}

-- Configurações de UI
GameConfig.UI_CONFIG = {
    NOTIFICATION_DURATION = 3,
    INVITE_TIMEOUT = 30,
    UPDATE_FREQUENCY = 0.1
}

-- Configurações de Gameplay
GameConfig.GAMEPLAY_CONFIG = {
    STARTING_RESOURCES = 0,
    RESOURCE_SPAWN_RATE = 1,
    PVP_ENABLED = true,
    BASE_PROTECTION_ENABLED = true
}

-- Configurações de Recursos
GameConfig.RESOURCE_CONFIG = {
    MAX_RESOURCES = 50,
    RESPAWN_TIME = 30,
    COLLECTION_RANGE = 100,
    SHRINK_RATE = 0.5,
    GROW_RATE = 1.0,
    MIN_SIZE_MULTIPLIER = 0.1
}

-- Configurações de Combate
GameConfig.COMBAT_CONFIG = {
    DAMAGE_AMOUNT = 25,
    WEAPON_RANGE = 500,
    FIRE_RATE = 0.3,
    RESPAWN_TIME_SOLO = 10,
    RESPAWN_TIME_TEAM = 15,
    BASE_SIZE_REDUCTION_ON_DEATH = 10
}

-- Configurações de Coleta
GameConfig.COLLECTION_CONFIG = {
    SLOWDOWN_MULTIPLIER = 0.3,
    NORMAL_WALKSPEED = 16,
    DEPOSIT_RANGE = 25,
    BEAM_WIDTH = 2
}

-- Configurações de Base
GameConfig.BASE_VISUAL_CONFIG = {
    MIN_BASE_SIZE = 10,
    MAX_BASE_SIZE = 500,
    DEFAULT_BASE_SIZE = 100,
    BARRIER_DAMAGE = 5,
    BARRIER_HEAL = 3,
    BARRIER_CHECK_INTERVAL = 1
}

-- Configurações de Ataque à Base
GameConfig.BASE_ATTACK_CONFIG = {
    DAMAGE_PER_SECOND = 2,
    ATTACK_RANGE = 150,
    UPDATE_INTERVAL = 0.5
}

-- Configurações de Construção
GameConfig.BUILDING_CONFIG = {
    RESOURCE_SPLIT_BASE_SIZE = 0.6, -- 60% para BaseSize
    RESOURCE_SPLIT_MATERIALS = 0.4, -- 40% para BuildingMaterials
    MAX_BUILDINGS_PER_BASE = 10,
    MIN_DISTANCE_FROM_TOWER = 8,
    MIN_DISTANCE_BETWEEN_BUILDINGS = 2
}

-- Configurações do HUD
GameConfig.HUD_CONFIG = {
    MAX_BASE_SIZE_DISPLAY = 500,
    UPDATE_FREQUENCY = 0.1,
    ANIMATION_DURATION = 0.3
}

-- Posições das bases no mapa (reduzido para 8 bases)
GameConfig.BASE_POSITIONS = {
    Vector3.new(100, 5, 100),
    Vector3.new(-100, 5, 100),
    Vector3.new(100, 5, -100),
    Vector3.new(-100, 5, -100),
    Vector3.new(200, 5, 0),
    Vector3.new(-200, 5, 0),
    Vector3.new(0, 5, 200),
    Vector3.new(0, 5, -200)
}

-- Função para validar configurações
function GameConfig.validateConfig()
    assert(#GameConfig.BASE_POSITIONS <= GameConfig.BASE_CONFIG.MAX_BASES, 
           "Número de posições de base excede o máximo permitido")
    
    assert(#GameConfig.TEAM_CONFIG.AVAILABLE_COLORS >= #GameConfig.BASE_POSITIONS,
           "Não há cores suficientes para todas as bases")
    
    print("Configurações do jogo validadas com sucesso!")
    return true
end

-- Função para obter configuração específica
function GameConfig.get(category, key)
    if GameConfig[category] and GameConfig[category][key] then
        return GameConfig[category][key]
    end
    warn("Configuração não encontrada: " .. category .. "." .. key)
    return nil
end

return GameConfig