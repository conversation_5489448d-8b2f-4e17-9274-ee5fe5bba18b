-- MainHUD.lua
-- Interface principal (HUD) que mostra todas as informações vitais do jogador

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Aguarda RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateBaseInfoEvent = remoteEventsFolder:WaitForChild("UpdateBaseInfo")
local depositResourceEvent = remoteEventsFolder:WaitForChild("DepositResource")

local MainHUD = {}

-- Variáveis da UI
local hudGui
local healthBar
local healthLabel
local baseSizeBar
local baseSizeLabel
local materialsIcon
local materialsLabel
local statusFrame

-- Cria o HUD principal
local function createMainHUD()
    -- ScreenGui principal
    hudGui = Instance.new("ScreenGui")
    hudGui.Name = "MainHUD"
    hudGui.ResetOnSpawn = false
    hudGui.Parent = playerGui
    
    -- Frame principal do HUD (canto superior esquerdo)
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 350, 0, 120)
    mainFrame.Position = UDim2.new(0, 20, 0, 20)
    mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    mainFrame.BorderSizePixel = 2
    mainFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    mainFrame.Parent = hudGui
    
    -- === BARRA DE VIDA ===
    local healthFrame = Instance.new("Frame")
    healthFrame.Name = "HealthFrame"
    healthFrame.Size = UDim2.new(1, -20, 0, 25)
    healthFrame.Position = UDim2.new(0, 10, 0, 10)
    healthFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthFrame.BorderSizePixel = 1
    healthFrame.BorderColor3 = Color3.new(0.4, 0.4, 0.4)
    healthFrame.Parent = mainFrame
    
    healthBar = Instance.new("Frame")
    healthBar.Name = "HealthBar"
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthFrame
    
    healthLabel = Instance.new("TextLabel")
    healthLabel.Name = "HealthLabel"
    healthLabel.Size = UDim2.new(1, 0, 1, 0)
    healthLabel.Position = UDim2.new(0, 0, 0, 0)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "VIDA: 100/100"
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSansBold
    healthLabel.Parent = healthFrame
    
    -- === BARRA DE TAMANHO DA BASE ===
    local baseSizeFrame = Instance.new("Frame")
    baseSizeFrame.Name = "BaseSizeFrame"
    baseSizeFrame.Size = UDim2.new(1, -20, 0, 25)
    baseSizeFrame.Position = UDim2.new(0, 10, 0, 45)
    baseSizeFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    baseSizeFrame.BorderSizePixel = 1
    baseSizeFrame.BorderColor3 = Color3.new(0.4, 0.4, 0.4)
    baseSizeFrame.Parent = mainFrame
    
    baseSizeBar = Instance.new("Frame")
    baseSizeBar.Name = "BaseSizeBar"
    baseSizeBar.Size = UDim2.new(0.5, 0, 1, 0)
    baseSizeBar.Position = UDim2.new(0, 0, 0, 0)
    baseSizeBar.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
    baseSizeBar.BorderSizePixel = 0
    baseSizeBar.Parent = baseSizeFrame
    
    baseSizeLabel = Instance.new("TextLabel")
    baseSizeLabel.Name = "BaseSizeLabel"
    baseSizeLabel.Size = UDim2.new(1, 0, 1, 0)
    baseSizeLabel.Position = UDim2.new(0, 0, 0, 0)
    baseSizeLabel.BackgroundTransparency = 1
    baseSizeLabel.Text = "BASE: 100/500"
    baseSizeLabel.TextColor3 = Color3.new(1, 1, 1)
    baseSizeLabel.TextScaled = true
    baseSizeLabel.Font = Enum.Font.SourceSansBold
    baseSizeLabel.Parent = baseSizeFrame
    
    -- === MATERIAIS DE CONSTRUÇÃO ===
    local materialsFrame = Instance.new("Frame")
    materialsFrame.Name = "MaterialsFrame"
    materialsFrame.Size = UDim2.new(1, -20, 0, 25)
    materialsFrame.Position = UDim2.new(0, 10, 0, 80)
    materialsFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    materialsFrame.BorderSizePixel = 1
    materialsFrame.BorderColor3 = Color3.new(0.4, 0.4, 0.4)
    materialsFrame.Parent = mainFrame
    
    -- Ícone de materiais
    materialsIcon = Instance.new("ImageLabel")
    materialsIcon.Name = "MaterialsIcon"
    materialsIcon.Size = UDim2.new(0, 20, 0, 20)
    materialsIcon.Position = UDim2.new(0, 5, 0, 2.5)
    materialsIcon.BackgroundColor3 = Color3.new(0.8, 0.6, 0.2)
    materialsIcon.BorderSizePixel = 0
    materialsIcon.Parent = materialsFrame
    
    -- Se não conseguir carregar imagem, usa cor sólida
    materialsIcon.Image = ""
    
    materialsLabel = Instance.new("TextLabel")
    materialsLabel.Name = "MaterialsLabel"
    materialsLabel.Size = UDim2.new(1, -35, 1, 0)
    materialsLabel.Position = UDim2.new(0, 30, 0, 0)
    materialsLabel.BackgroundTransparency = 1
    materialsLabel.Text = "MATERIAIS: 0"
    materialsLabel.TextColor3 = Color3.new(1, 1, 1)
    materialsLabel.TextScaled = true
    materialsLabel.Font = Enum.Font.SourceSansBold
    materialsLabel.TextXAlignment = Enum.TextXAlignment.Left
    materialsLabel.Parent = materialsFrame
    
    -- === FRAME DE STATUS ===
    statusFrame = Instance.new("Frame")
    statusFrame.Name = "StatusFrame"
    statusFrame.Size = UDim2.new(0, 300, 0, 80)
    statusFrame.Position = UDim2.new(0, 20, 0, 160)
    statusFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    statusFrame.BorderSizePixel = 2
    statusFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    statusFrame.Visible = false
    statusFrame.Parent = hudGui
    
    local statusLabel = Instance.new("TextLabel")
    statusLabel.Name = "StatusLabel"
    statusLabel.Size = UDim2.new(1, -20, 1, -20)
    statusLabel.Position = UDim2.new(0, 10, 0, 10)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = ""
    statusLabel.TextColor3 = Color3.new(1, 1, 1)
    statusLabel.TextScaled = true
    statusLabel.Font = Enum.Font.SourceSans
    statusLabel.TextWrapped = true
    statusLabel.Parent = statusFrame
end

-- Atualiza a barra de vida
local function updateHealthBar()
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        local healthPercent = humanoid.Health / humanoid.MaxHealth
        
        -- Anima a barra de vida
        local tween = TweenService:Create(
            healthBar,
            TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {Size = UDim2.new(healthPercent, 0, 1, 0)}
        )
        tween:Play()
        
        healthLabel.Text = "VIDA: " .. math.floor(humanoid.Health) .. "/" .. math.floor(humanoid.MaxHealth)
        
        -- Muda cor baseada na vida
        if healthPercent > 0.6 then
            healthBar.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
        elseif healthPercent > 0.3 then
            healthBar.BackgroundColor3 = Color3.new(0.8, 0.8, 0.2)
        else
            healthBar.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
        end
    end
end

-- Atualiza informações da base
local function updateBaseInfo()
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            local baseSizeValue = base:FindFirstChild("BaseSize")
            local buildingMaterialsValue = base:FindFirstChild("BuildingMaterials")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                -- Atualiza barra de tamanho da base
                if baseSizeValue then
                    local maxBaseSize = 500 -- Configurável
                    local sizePercent = math.min(1, baseSizeValue.Value / maxBaseSize)
                    
                    local tween = TweenService:Create(
                        baseSizeBar,
                        TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                        {Size = UDim2.new(sizePercent, 0, 1, 0)}
                    )
                    tween:Play()
                    
                    baseSizeLabel.Text = "BASE: " .. math.floor(baseSizeValue.Value) .. "/" .. maxBaseSize
                    
                    -- Muda cor baseada no tamanho
                    if sizePercent > 0.7 then
                        baseSizeBar.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
                    elseif sizePercent > 0.3 then
                        baseSizeBar.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
                    else
                        baseSizeBar.BackgroundColor3 = Color3.new(0.8, 0.6, 0.2)
                    end
                end
                
                -- Atualiza materiais de construção
                if buildingMaterialsValue then
                    materialsLabel.Text = "MATERIAIS: " .. buildingMaterialsValue.Value
                    
                    -- Anima o ícone quando materiais mudam
                    local currentMaterials = tonumber(materialsLabel.Text:match("%d+")) or 0
                    if buildingMaterialsValue.Value ~= currentMaterials then
                        local tween = TweenService:Create(
                            materialsIcon,
                            TweenInfo.new(0.2, Enum.EasingStyle.Elastic, Enum.EasingDirection.Out),
                            {Size = UDim2.new(0, 25, 0, 25)}
                        )
                        tween:Play()
                        
                        tween.Completed:Connect(function()
                            local tween2 = TweenService:Create(
                                materialsIcon,
                                TweenInfo.new(0.2, Enum.EasingStyle.Elastic, Enum.EasingDirection.Out),
                                {Size = UDim2.new(0, 20, 0, 20)}
                            )
                            tween2:Play()
                        end)
                    end
                end
                
                return -- Encontrou a base, sai da função
            end
        end
    end
    
    -- Se não encontrou base, mostra valores padrão
    baseSizeLabel.Text = "BASE: Sem Base"
    baseSizeBar.Size = UDim2.new(0, 0, 1, 0)
    materialsLabel.Text = "MATERIAIS: 0"
end

-- Mostra status temporário
local function showStatus(message, duration, color)
    local statusLabel = statusFrame:FindFirstChild("StatusLabel")
    if statusLabel then
        statusLabel.Text = message
        statusFrame.BackgroundColor3 = color or Color3.new(0.1, 0.1, 0.1)
        statusFrame.Visible = true
        
        -- Anima entrada
        statusFrame.Position = UDim2.new(0, -320, 0, 160)
        local tweenIn = TweenService:Create(
            statusFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
            {Position = UDim2.new(0, 20, 0, 160)}
        )
        tweenIn:Play()
        
        -- Remove após duração
        spawn(function()
            wait(duration or 3)
            local tweenOut = TweenService:Create(
                statusFrame,
                TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
                {Position = UDim2.new(0, -320, 0, 160)}
            )
            tweenOut:Play()
            tweenOut.Completed:Connect(function()
                statusFrame.Visible = false
            end)
        end)
    end
end

-- Conecta eventos
player.CharacterAdded:Connect(function(character)
    local humanoid = character:WaitForChild("Humanoid")
    
    -- Conecta mudanças de vida
    humanoid.HealthChanged:Connect(updateHealthBar)
    
    -- Atualiza imediatamente
    updateHealthBar()
end)

-- Conecta evento de depósito
depositResourceEvent.OnClientEvent:Connect(function(value, newBaseSize, newMaterials)
    showStatus(
        "Recurso depositado!\n+" .. math.floor(value * 0.6) .. " Base Size\n+" .. math.floor(value * 0.4) .. " Materiais",
        2,
        Color3.new(0.2, 0.8, 0.2)
    )
end)

-- Loop de atualização
RunService.Heartbeat:Connect(function()
    updateBaseInfo()
end)

-- Inicializa o HUD
createMainHUD()

-- Atualiza se já tem character
if player.Character then
    updateHealthBar()
end

print("MainHUD inicializado com sucesso!")

return MainHUD