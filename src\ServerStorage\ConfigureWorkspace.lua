-- ConfigureWorkspace.lua
-- Script para configurar o Workspace adequadamente

local Workspace = game:GetService("Workspace")
local Lighting = game:GetService("Lighting")

local function configureWorkspace()
    -- Configurações básicas do Workspace
    Workspace.Gravity = 196.2 -- Gravidade padrão
    -- FallenPartsDestroyHeight requer permissões especiais, pular
    
    -- Remove objetos padrão desnecessários que podem interferir
    for _, obj in ipairs(Workspace:GetChildren()) do
        if obj.Name == "Baseplate" and obj.Size == Vector3.new(512, 20, 512) then
            obj:Destroy() -- Remove baseplate padrão pequeno
        elseif obj.Name == "SpawnLocation" and obj.Size == Vector3.new(6, 1, 6) then
            obj:Destroy() -- Remove spawn padrão
        end
    end
    
    -- Configurações de iluminação básicas
    Lighting.Brightness = 2
    Lighting.Ambient = Color3.new(0.2, 0.2, 0.2)
    Lighting.TimeOfDay = "14:00:00"
    Lighting.FogEnd = 100000
    Lighting.FogStart = 0
    
    -- Remove skybox existente se houver
    for _, obj in ipairs(Lighting:GetChildren()) do
        if obj:IsA("Sky") then
            obj:Destroy()
        end
    end
    
    print("Workspace configurado com sucesso!")
    print("- Gravidade: 196.2")
    print("- Objetos padrão removidos")
    print("- Iluminação configurada")
end

-- Executa a configuração
configureWorkspace()

-- Retorna true para indicar sucesso
return true