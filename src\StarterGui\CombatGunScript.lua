-- CombatGunScript.lua
-- LocalScript para controlar a CombatGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

local player = Players.LocalPlayer

-- Aguarda os RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireWeaponEvent = remoteEventsFolder:WaitForChild("FireWeapon")

-- Configurações da arma
local FIRE_RATE = 0.3
local PROJECTILE_SPEED = 200
local RANGE = 500

-- Variáveis de controle
local lastFireTime = 0
local equipped = false
local currentTool = nil

-- Função para criar projétil visual
local function createProjectile(startPos, direction)
    local projectile = Instance.new("Part")
    projectile.Name = "Projectile"
    projectile.Size = Vector3.new(0.2, 0.2, 1)
    projectile.BrickColor = BrickColor.new("Bright yellow")
    projectile.Material = Enum.Material.Neon
    projectile.CanCollide = false
    projectile.Anchored = true
    projectile.Parent = workspace
    
    local startTime = tick()
    local connection
    connection = RunService.Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local distance = elapsed * PROJECTILE_SPEED
        
        if distance > RANGE then
            connection:Disconnect()
            projectile:Destroy()
            return
        end
        
        projectile.Position = startPos + direction * distance
        projectile.CFrame = CFrame.lookAt(projectile.Position, projectile.Position + direction)
    end)
    
    -- Remove projétil após 3 segundos
    Debris:AddItem(projectile, 3)
end

-- Função para disparar
local function fire()
    local currentTime = tick()
    if currentTime - lastFireTime < FIRE_RATE then
        return
    end
    
    lastFireTime = currentTime
    
    if not currentTool or not currentTool.Parent then
        return
    end
    
    local character = currentTool.Parent
    local head = character:FindFirstChild("Head")
    
    if not head then return end
    
    local mouse = player:GetMouse()
    local startPos = head.Position
    local direction = (mouse.Hit.Position - startPos).Unit
    
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
    
    createProjectile(startPos, direction)
    
    if raycastResult then
        fireWeaponEvent:FireServer(raycastResult.Position, raycastResult.Instance)
    else
        fireWeaponEvent:FireServer(startPos + direction * RANGE, nil)
    end
    
    local sound = currentTool.Handle:FindFirstChild("FireSound")
    if sound then
        sound:Play()
    end
end

-- Conecta eventos quando a ferramenta é equipada
local function onToolEquipped(tool)
    if tool.Name ~= "CombatGun" then return end
    
    equipped = true
    currentTool = tool
    
    local connection
    connection = tool.Activated:Connect(function()
        if equipped then
            fire()
        end
    end)
    
    tool.Unequipped:Connect(function()
        equipped = false
        currentTool = nil
        if connection then
            connection:Disconnect()
        end
    end)
end

-- Conecta eventos para ferramentas já existentes e futuras
local function connectToTools()
    -- Para ferramentas já no StarterPack
    local starterPack = game:GetService("StarterPack")
    for _, tool in ipairs(starterPack:GetChildren()) do
        if tool:IsA("Tool") and tool.Name == "CombatGun" then
            tool.Equipped:Connect(function()
                onToolEquipped(tool)
            end)
        end
    end
    
    -- Para ferramentas adicionadas ao StarterPack
    starterPack.ChildAdded:Connect(function(child)
        if child:IsA("Tool") and child.Name == "CombatGun" then
            child.Equipped:Connect(function()
                onToolEquipped(child)
            end)
        end
    end)
end

-- Conecta para o jogador atual
player.CharacterAdded:Connect(function(character)
    wait(1) -- Aguarda o character carregar
    
    -- Conecta para ferramentas no backpack
    local backpack = player:WaitForChild("Backpack")
    for _, tool in ipairs(backpack:GetChildren()) do
        if tool:IsA("Tool") and tool.Name == "CombatGun" then
            tool.Equipped:Connect(function()
                onToolEquipped(tool)
            end)
        end
    end
    
    backpack.ChildAdded:Connect(function(child)
        if child:IsA("Tool") and child.Name == "CombatGun" then
            child.Equipped:Connect(function()
                onToolEquipped(child)
            end)
        end
    end)
end)

-- Conecta para ferramentas já existentes
connectToTools()

print("CombatGunScript inicializado!")
