-- BaseManager.lua
-- <PERSON>ript responsável por gerenciar as bases reivindicáveis no jogo

local ServerStorage = game:GetService("ServerStorage")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- <PERSON>ega as configurações do jogo
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))
GameConfig.validateConfig()

-- Carrega o BaseController
local BaseController = require(script.Parent.BaseController)

local BaseManager = {}

-- Obtém configurações das configurações centralizadas
local TEAM_COLORS = {}
for _, colorName in ipairs(GameConfig.TEAM_CONFIG.AVAILABLE_COLORS) do
    local success, color = pcall(function()
        return BrickColor.new(colorName)
    end)
    if success then
        table.insert(TEAM_COLORS, color)
        print("Cor adicionada: " .. colorName .. " -> " .. tostring(color))
    else
        warn("Cor inválida: " .. colorName)
    end
end

local BASE_POSITIONS = GameConfig.BASE_POSITIONS

local activeBases = {}
local availableColors = {}
local playerBases = {} -- Mapeia jogador para sua base

-- Inicializa as cores disponíveis
local function initializeColors()
    availableColors = {} -- Limpa a lista primeiro
    for _, color in ipairs(TEAM_COLORS) do
        table.insert(availableColors, color)
    end
    print("Cores inicializadas: " .. #availableColors .. " cores disponíveis")
    for i, color in ipairs(availableColors) do
        print("  " .. i .. ": " .. tostring(color))
    end
end

-- Cria uma base individual
local function createBase(position, index)
    local baseTemplate = ServerStorage:FindFirstChild("BaseTemplate")
    if not baseTemplate then
        warn("BaseTemplate não encontrado em ServerStorage!")
        return nil
    end
    
    local base = baseTemplate:Clone()
    base.Name = "Base_" .. index
    base.Parent = workspace
    
    -- Posiciona a base
    if base.PrimaryPart then
        base:SetPrimaryPartCFrame(CFrame.new(position))
    else
        -- Se não há PrimaryPart, move manualmente todos os componentes
        local basePlatform = base:FindFirstChild("BasePlatform")
        local coreTower = base:FindFirstChild("CoreTower")
        local spawnLocation = base:FindFirstChild("SpawnLocation")
        local barrier = base:FindFirstChild("Barrier")
        local claimPad = base:FindFirstChild("ClaimPad")

        if basePlatform then
            local offset = position - basePlatform.Position

            -- Move todos os componentes com o mesmo offset
            basePlatform.Position = position

            if coreTower then
                coreTower.Position = coreTower.Position + offset
                -- Move também a bandeira e haste
                local flag = coreTower:FindFirstChild("Flag")
                local flagPole = coreTower:FindFirstChild("FlagPole")
                if flag then flag.Position = flag.Position + offset end
                if flagPole then flagPole.Position = flagPole.Position + offset end
            end

            if spawnLocation then
                spawnLocation.Position = spawnLocation.Position + offset
            end

            if barrier then
                barrier.Position = barrier.Position + offset
            end

            if claimPad then
                claimPad.Position = claimPad.Position + offset
                print("ClaimPad posicionado em: " .. tostring(claimPad.Position))
            end
        end
    end
    
    return base
end

-- Atribui uma cor à base e ao jogador
local function assignColorToBase(base, player)
    if #availableColors == 0 then
        warn("Não há cores disponíveis!")
        return false
    end

    local color = table.remove(availableColors, 1)
    print("Atribuindo cor " .. tostring(color) .. " à base " .. base.Name .. " para jogador " .. player.Name)

    -- Muda a cor dos componentes da base
    local basePlatform = base:FindFirstChild("BasePlatform")
    local coreTower = base:FindFirstChild("CoreTower")
    local barrier = base:FindFirstChild("Barrier")

    if basePlatform then
        basePlatform.BrickColor = color
        print("  BasePlatform colorida: " .. tostring(color))
    end

    if coreTower then
        coreTower.BrickColor = color
        print("  CoreTower colorida: " .. tostring(color))

        -- Muda a cor da bandeira também
        local flag = coreTower:FindFirstChild("Flag")
        if flag then
            flag.BrickColor = color
            print("  Flag colorida: " .. tostring(color))
        else
            warn("  Flag não encontrada na CoreTower!")
        end
    end

    if barrier then
        barrier.BrickColor = color
        print("  Barrier colorida: " .. tostring(color))
    end
    
    -- Cria uma bandeira pequena nas costas do jogador em vez de mudar a cor do corpo
    createPlayerFlag(player, color)
    
    return true
end

-- Configura o evento de reivindicação da base
local function setupClaimPad(base)
    local claimPad = base:FindFirstChild("ClaimPad")
    local owner = base:FindFirstChild("Owner")
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    
    if not claimPad or not owner or not spawnLocation then
        warn("Componentes da base não encontrados!")
        return
    end
    
    local connection
    connection = claimPad.Touched:Connect(function(hit)
        local humanoid = hit.Parent:FindFirstChild("Humanoid")
        if not humanoid then return end
        
        local player = Players:GetPlayerFromCharacter(hit.Parent)
        if not player then return end
        
        -- Verifica se a base já tem dono
        if owner.Value ~= nil then return end
        
        -- Verifica se o jogador já tem uma base
        if playerBases[player] then return end
        
        -- Verifica se o jogador tem RespawnLocation (já tem base)
        if player.RespawnLocation then return end
        
        -- Reivindica a base
        owner.Value = player
        playerBases[player] = base
        
        -- Atribui cor à base e ao jogador
        if assignColorToBase(base, player) then
            -- Ativa o spawn da base
            spawnLocation.Enabled = true
            player.RespawnLocation = spawnLocation
            
            print(player.Name .. " reivindicou a base " .. base.Name)
            
            -- Desconecta o evento de toque
            connection:Disconnect()
        else
            -- Se não conseguiu atribuir cor, reverte a reivindicação
            owner.Value = nil
            playerBases[player] = nil
        end
    end)
end

-- Limpa a base quando o jogador sai
local function cleanupPlayerBase(player)
    local base = playerBases[player]
    if not base then return end
    
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    
    -- Se o jogador era o dono
    if owner and owner.Value == player then
        -- Se há um parceiro, promove ele a dono
        if partner and partner.Value then
            owner.Value = partner.Value
            partner.Value = nil
            playerBases[partner.Value] = base
        else
            -- Libera a base completamente
            owner.Value = nil
            spawnLocation.Enabled = false
            
            -- Retorna a cor para a lista de disponíveis
            local basePlatform = base:FindFirstChild("BasePlatform")
            if basePlatform then
                table.insert(availableColors, basePlatform.BrickColor)
            end
            
            -- Reativa o ClaimPad
            setupClaimPad(base)
        end
    elseif partner and partner.Value == player then
        -- Se o jogador era apenas parceiro
        partner.Value = nil
    end
    
    playerBases[player] = nil
end

-- Inicializa o sistema de bases
local function initializeBases()
    initializeColors()
    
    for i, position in ipairs(BASE_POSITIONS) do
        local base = createBase(position, i)
        if base then
            table.insert(activeBases, base)
            setupClaimPad(base)
        end
    end
    
    print("Sistema de bases inicializado com " .. #activeBases .. " bases")
end

-- Função para criar bandeira nas costas do jogador
local function createPlayerFlag(player, color)
    if not player.Character then return end

    local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
    if humanoidRootPart then
        -- Remove bandeira anterior se existir
        local oldFlag = humanoidRootPart:FindFirstChild("TeamFlag")
        if oldFlag then oldFlag:Destroy() end

        -- Cria nova bandeira
        local teamFlag = Instance.new("Part")
        teamFlag.Name = "TeamFlag"
        teamFlag.Size = Vector3.new(0.1, 2, 3)
        teamFlag.BrickColor = color
        teamFlag.Material = Enum.Material.Fabric
        teamFlag.CanCollide = false
        teamFlag.Parent = humanoidRootPart

        -- Posiciona a bandeira nas costas
        local weld = Instance.new("WeldConstraint")
        weld.Part0 = humanoidRootPart
        weld.Part1 = teamFlag
        weld.Parent = humanoidRootPart

        teamFlag.CFrame = humanoidRootPart.CFrame * CFrame.new(0, 1, -1.5)

        print("Bandeira de equipe criada para " .. player.Name .. " com cor " .. tostring(color))
    end
end

-- Função para garantir que a bandeira seja recriada quando o jogador spawnar
local function onPlayerSpawned(player)
    local base = playerBases[player]
    if base then
        local basePlatform = base:FindFirstChild("BasePlatform")
        if basePlatform then
            wait(1) -- Aguarda o character carregar completamente
            createPlayerFlag(player, basePlatform.BrickColor)
        end
    end
end

-- Conecta eventos de jogadores
Players.PlayerRemoving:Connect(cleanupPlayerBase)

-- Conecta evento de spawn para recriar bandeiras
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function()
        onPlayerSpawned(player)
    end)
end)

-- Para jogadores já no jogo
for _, player in ipairs(Players:GetPlayers()) do
    if not player.CharacterAdded:IsConnected() then
        player.CharacterAdded:Connect(function()
            onPlayerSpawned(player)
        end)
    end
end

-- Sistema de efeitos da barreira
local BARRIER_DAMAGE = 5 -- Dano por segundo em barreira inimiga
local BARRIER_HEAL = 3 -- Cura por segundo em barreira própria
local BARRIER_CHECK_INTERVAL = 1 -- Segundos entre verificações

-- Função para aplicar efeitos da barreira
local function applyBarrierEffects()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("Humanoid") then
            local humanoid = player.Character.Humanoid
            
            -- Verifica todas as bases
            for _, base in ipairs(activeBases) do
                if base and base.Parent then
                    local isInBarrier = BaseController.IsPlayerInBarrier(player, base)
                    
                    if isInBarrier then
                        local isTeammate = BaseController.IsPlayerTeammate(player, base)
                        
                        if isTeammate then
                            -- Cura jogador da própria equipe
                            if humanoid.Health < humanoid.MaxHealth then
                                humanoid.Health = math.min(humanoid.MaxHealth, humanoid.Health + BARRIER_HEAL)
                                
                                -- Efeito visual de cura melhorado
                                local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
                                if humanoidRootPart then
                                    local healEffect = humanoidRootPart:FindFirstChild("HealEffect")
                                    if not healEffect then
                                        local attachment = Instance.new("Attachment")
                                        attachment.Name = "HealEffect"
                                        attachment.Parent = humanoidRootPart
                                        
                                        -- Partículas de cura principais
                                        local particles = Instance.new("ParticleEmitter")
                                        particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
                                        particles.Color = ColorSequence.new{
                                            ColorSequenceKeypoint.new(0, Color3.new(0, 1, 0)),
                                            ColorSequenceKeypoint.new(0.5, Color3.new(0.5, 1, 0.5)),
                                            ColorSequenceKeypoint.new(1, Color3.new(1, 1, 1))
                                        }
                                        particles.Size = NumberSequence.new{
                                            NumberSequenceKeypoint.new(0, 0.5),
                                            NumberSequenceKeypoint.new(0.5, 1),
                                            NumberSequenceKeypoint.new(1, 0)
                                        }
                                        particles.Lifetime = NumberRange.new(1, 2)
                                        particles.Rate = 20
                                        particles.SpreadAngle = Vector2.new(360, 360)
                                        particles.Speed = NumberRange.new(2, 5)
                                        particles.Acceleration = Vector3.new(0, 5, 0)
                                        particles.Parent = attachment
                                        
                                        -- Efeito de aura ao redor do jogador
                                        local aura = Instance.new("ParticleEmitter")
                                        aura.Texture = "rbxasset://textures/particles/smoke_main.dds"
                                        aura.Color = ColorSequence.new(Color3.new(0.5, 1, 0.5))
                                        aura.Size = NumberSequence.new(2)
                                        aura.Lifetime = NumberRange.new(2, 3)
                                        aura.Rate = 5
                                        aura.SpreadAngle = Vector2.new(180, 180)
                                        aura.Speed = NumberRange.new(0.5, 1)
                                        aura.Transparency = NumberSequence.new{
                                            NumberSequenceKeypoint.new(0, 0.8),
                                            NumberSequenceKeypoint.new(0.5, 0.5),
                                            NumberSequenceKeypoint.new(1, 1)
                                        }
                                        aura.Parent = attachment

                                        -- Efeito de círculo de cura no chão
                                        local healCircle = Instance.new("Part")
                                        healCircle.Name = "HealCircle"
                                        healCircle.Size = Vector3.new(8, 0.1, 8)
                                        healCircle.Position = humanoidRootPart.Position - Vector3.new(0, 3, 0)
                                        healCircle.BrickColor = BrickColor.new("Lime green")
                                        healCircle.Material = Enum.Material.Neon
                                        healCircle.Transparency = 0.7
                                        healCircle.CanCollide = false
                                        healCircle.Anchored = true
                                        healCircle.Shape = Enum.PartType.Cylinder
                                        healCircle.Parent = workspace

                                        -- Rotaciona o círculo para ficar horizontal
                                        healCircle.CFrame = healCircle.CFrame * CFrame.Angles(0, 0, math.rad(90))

                                        -- Remove o círculo após um tempo
                                        game:GetService("Debris"):AddItem(healCircle, 2)

                                        -- Som de cura
                                        local healSound = Instance.new("Sound")
                                        healSound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
                                        healSound.Volume = 0.3
                                        healSound.Pitch = 1.5
                                        healSound.Parent = humanoidRootPart
                                        healSound:Play()

                                        game:GetService("Debris"):AddItem(healSound, 2)
                                    end
                                end
                            end
                        else
                            -- Dano a jogador inimigo
                            local owner = base:FindFirstChild("Owner")
                            if owner and owner.Value then -- Só causa dano se a base tem dono
                                humanoid.Health = math.max(0, humanoid.Health - BARRIER_DAMAGE)
                                
                                -- Efeito visual de dano
                                local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
                                if humanoidRootPart then
                                    local damageEffect = humanoidRootPart:FindFirstChild("DamageEffect")
                                    if not damageEffect then
                                        local attachment = Instance.new("Attachment")
                                        attachment.Name = "DamageEffect"
                                        attachment.Parent = humanoidRootPart
                                        
                                        local particles = Instance.new("ParticleEmitter")
                                        particles.Texture = "rbxasset://textures/particles/fire_main.dds"
                                        particles.Color = ColorSequence.new(Color3.new(1, 0, 0))
                                        particles.Size = NumberSequence.new(0.5)
                                        particles.Lifetime = NumberRange.new(0.3, 0.8)
                                        particles.Rate = 15
                                        particles.SpreadAngle = Vector2.new(30, 30)
                                        particles.Speed = NumberRange.new(2, 5)
                                        particles.Parent = attachment
                                    end
                                end
                                
                                print(player.Name .. " está recebendo dano na barreira inimiga!")
                            end
                        end
                        break -- Sai do loop se encontrou uma barreira
                    else
                        -- Remove efeitos visuais se não está em nenhuma barreira
                        local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
                        if humanoidRootPart then
                            local healEffect = humanoidRootPart:FindFirstChild("HealEffect")
                            local damageEffect = humanoidRootPart:FindFirstChild("DamageEffect")
                            if healEffect then healEffect:Destroy() end
                            if damageEffect then damageEffect:Destroy() end
                        end
                    end
                end
            end
        end
    end
end

-- Loop de verificação dos efeitos da barreira
spawn(function()
    while true do
        wait(BARRIER_CHECK_INTERVAL)
        applyBarrierEffects()
    end
end)

-- Função para atualizar tamanho da base (chamada externamente)
function BaseManager.updateBaseSize(base)
    return BaseController.UpdateBaseSize(base)
end

-- Função para obter base de um jogador (para outros scripts)
function BaseManager.getPlayerBase(player)
    return playerBases[player]
end

-- Inicializa quando o script carrega
initializeBases()

return BaseManager