-- DashSystem.lua
-- Sistema de dash para todas as direções com efeitos visuais

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Aguarda RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local dashEvent = remoteEventsFolder:WaitForChild("Dash")

local DashSystem = {}

-- Configurações do dash
local DASH_FORCE = 50
local DASH_COOLDOWN = 5 -- 5 segundos
local DASH_DURATION = 0.3

-- Variáveis de controle
local lastDashTime = 0
local isDashing = false

-- UI do cooldown
local dashUI
local cooldownFrame
local cooldownLabel

-- Cria a UI do dash
local function createDashUI()
    dashUI = Instance.new("ScreenGui")
    dashUI.Name = "DashUI"
    dashUI.ResetOnSpawn = false
    dashUI.Parent = playerGui
    
    cooldownFrame = Instance.new("Frame")
    cooldownFrame.Name = "CooldownFrame"
    cooldownFrame.Size = UDim2.new(0, 100, 0, 100)
    cooldownFrame.Position = UDim2.new(1, -120, 1, -120)
    cooldownFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    cooldownFrame.BorderSizePixel = 2
    cooldownFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    cooldownFrame.Visible = false
    cooldownFrame.Parent = dashUI
    
    cooldownLabel = Instance.new("TextLabel")
    cooldownLabel.Name = "CooldownLabel"
    cooldownLabel.Size = UDim2.new(1, 0, 1, 0)
    cooldownLabel.Position = UDim2.new(0, 0, 0, 0)
    cooldownLabel.BackgroundTransparency = 1
    cooldownLabel.Text = "DASH\nREADY"
    cooldownLabel.TextColor3 = Color3.new(1, 1, 1)
    cooldownLabel.TextScaled = true
    cooldownLabel.Font = Enum.Font.SourceSansBold
    cooldownLabel.Parent = cooldownFrame
    
    -- Círculo de progresso
    local progressCircle = Instance.new("Frame")
    progressCircle.Name = "ProgressCircle"
    progressCircle.Size = UDim2.new(0.8, 0, 0.8, 0)
    progressCircle.Position = UDim2.new(0.1, 0, 0.1, 0)
    progressCircle.BackgroundColor3 = Color3.new(0, 0.8, 1)
    progressCircle.BorderSizePixel = 0
    progressCircle.Parent = cooldownFrame
    
    -- Torna o círculo redondo
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0.5, 0)
    corner.Parent = progressCircle
end

-- Função para obter direção do movimento
local function getMovementDirection()
    local camera = workspace.CurrentCamera
    local character = player.Character
    if not character or not camera then return Vector3.new(0, 0, 0) end
    
    local humanoid = character:FindFirstChild("Humanoid")
    if not humanoid then return Vector3.new(0, 0, 0) end
    
    local moveVector = humanoid.MoveDirection
    if moveVector.Magnitude == 0 then
        -- Se não está se movendo, dash para frente baseado na câmera
        return camera.CFrame.LookVector
    else
        -- Dash na direção do movimento
        return moveVector
    end
end

-- Função para criar efeitos visuais do dash
local function createDashEffects(character, direction)
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Efeito de partículas
    local attachment = Instance.new("Attachment")
    attachment.Name = "DashEffect"
    attachment.Parent = humanoidRootPart
    
    local particles = Instance.new("ParticleEmitter")
    particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
    particles.Color = ColorSequence.new(Color3.new(0, 0.8, 1))
    particles.Size = NumberSequence.new{
        NumberSequenceKeypoint.new(0, 1),
        NumberSequenceKeypoint.new(1, 0)
    }
    particles.Lifetime = NumberRange.new(0.3, 0.8)
    particles.Rate = 100
    particles.SpreadAngle = Vector2.new(45, 45)
    particles.Speed = NumberRange.new(10, 20)
    particles.Parent = attachment
    
    -- Efeito de rastro
    local trail = Instance.new("Trail")
    trail.Color = ColorSequence.new(Color3.new(0, 0.8, 1))
    trail.Transparency = NumberSequence.new{
        NumberSequenceKeypoint.new(0, 0.3),
        NumberSequenceKeypoint.new(1, 1)
    }
    trail.Lifetime = 0.5
    trail.MinLength = 0
    trail.Parent = humanoidRootPart
    
    -- Attachments para o trail
    local attachment0 = Instance.new("Attachment")
    attachment0.Position = Vector3.new(-1, 0, 0)
    attachment0.Parent = humanoidRootPart
    
    local attachment1 = Instance.new("Attachment")
    attachment1.Position = Vector3.new(1, 0, 0)
    attachment1.Parent = humanoidRootPart
    
    trail.Attachment0 = attachment0
    trail.Attachment1 = attachment1
    
    -- Remove efeitos após o dash
    spawn(function()
        wait(DASH_DURATION + 0.5)
        if attachment then attachment:Destroy() end
        if trail then trail:Destroy() end
        if attachment0 then attachment0:Destroy() end
        if attachment1 then attachment1:Destroy() end
    end)
    
    -- Som do dash
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.5
    sound.Pitch = 2
    sound.Parent = humanoidRootPart
    sound:Play()
    
    game:GetService("Debris"):AddItem(sound, 2)
end

-- Função para executar o dash
local function performDash()
    local currentTime = tick()
    
    -- Verifica cooldown
    if currentTime - lastDashTime < DASH_COOLDOWN then
        return false
    end
    
    -- Verifica se o jogador existe
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    -- Verifica se já está dashando
    if isDashing then
        return false
    end
    
    local character = player.Character
    local humanoidRootPart = character.HumanoidRootPart
    local humanoid = character:FindFirstChild("Humanoid")
    
    if not humanoid then return false end
    
    -- Obtém direção do dash
    local direction = getMovementDirection()
    if direction.Magnitude == 0 then
        direction = humanoidRootPart.CFrame.LookVector
    end
    
    -- Normaliza a direção e remove componente Y
    direction = Vector3.new(direction.X, 0, direction.Z).Unit
    
    -- Marca como dashando
    isDashing = true
    lastDashTime = currentTime
    
    -- Cria efeitos visuais
    createDashEffects(character, direction)
    
    -- Envia para o servidor
    dashEvent:FireServer(direction)
    
    -- Atualiza UI
    updateCooldownUI()
    
    -- Para de dashar após a duração
    spawn(function()
        wait(DASH_DURATION)
        isDashing = false
    end)
    
    return true
end

-- Atualiza a UI do cooldown
function updateCooldownUI()
    cooldownFrame.Visible = true
    
    local function updateTimer()
        local currentTime = tick()
        local timeLeft = DASH_COOLDOWN - (currentTime - lastDashTime)
        
        if timeLeft <= 0 then
            cooldownLabel.Text = "DASH\nREADY"
            cooldownLabel.TextColor3 = Color3.new(0, 1, 0)
            cooldownFrame.Visible = false
            return
        end
        
        cooldownLabel.Text = "DASH\n" .. math.ceil(timeLeft)
        cooldownLabel.TextColor3 = Color3.new(1, 0.5, 0)
        
        -- Atualiza círculo de progresso
        local progress = 1 - (timeLeft / DASH_COOLDOWN)
        local progressCircle = cooldownFrame:FindFirstChild("ProgressCircle")
        if progressCircle then
            progressCircle.Size = UDim2.new(0.8 * progress, 0, 0.8, 0)
        end
    end
    
    -- Loop de atualização
    local connection
    connection = RunService.Heartbeat:Connect(function()
        updateTimer()
        if tick() - lastDashTime >= DASH_COOLDOWN then
            connection:Disconnect()
        end
    end)
end

-- Conecta controles
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    -- Shift para dash
    if input.KeyCode == Enum.KeyCode.LeftShift or input.KeyCode == Enum.KeyCode.RightShift then
        performDash()
    end
end)

-- Conecta evento de respawn
player.CharacterAdded:Connect(function(character)
    -- Reseta cooldown quando respawna
    lastDashTime = 0
    isDashing = false
    if cooldownFrame then
        cooldownFrame.Visible = false
    end
end)

-- Inicializa a UI
createDashUI()

print("DashSystem inicializado com sucesso!")
print("Use SHIFT para fazer dash!")

return DashSystem