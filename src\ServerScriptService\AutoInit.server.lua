-- AutoInit.server.lua
-- Script que executa automaticamente quando o servidor carrega

local ServerStorage = game:GetService("ServerStorage")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda um pouco para garantir que todos os scripts foram carregados
wait(2)

print("=== AUTO-INICIALIZANDO JOGO DE ARENA - FASE 4 ===")

-- Função para executar com tratamento de erro
local function safeRequire(module, name)
    local success, result = pcall(function()
        return require(module)
    end)
    
    if success then
        print("✅ " .. name .. " - Sucesso")
    else
        warn("❌ " .. name .. " - Erro: " .. tostring(result))
    end
    
    return success
end

-- 1. Configura o Workspace primeiro
print("1. Configurando Workspace...")
safeRequire(ServerStorage.ConfigureWorkspace, "ConfigureWorkspace")

-- 2. Cria o mapa básico
print("2. Criando mapa básico...")
safeRequire(ServerStorage.CreateBasicMap, "CreateBasicMap")

-- 3. Cria os RemoteEvents
print("3. Criando RemoteEvents...")
safeRequire(ReplicatedStorage.RemoteEvents, "RemoteEvents")

-- 4. Cria o BaseTemplate
print("4. Criando BaseTemplate...")
safeRequire(ServerStorage.CreateBaseTemplate, "CreateBaseTemplate")

-- 5. Cria as ferramentas (CombatGun e CollectorGun)
print("5. Criando ferramentas...")
safeRequire(ServerStorage.CreateToolsFixed, "CreateToolsFixed")

-- 6. Configura o StarterPlayer
print("6. Configurando StarterPlayer...")
safeRequire(ServerStorage.ConfigureStarterPlayer, "ConfigureStarterPlayer")

-- 7. Aguarda um pouco para garantir que tudo foi criado
wait(2)

-- 8. Inicializa o sistema de bases
print("8. Inicializando BaseManager...")
safeRequire(script.Parent.BaseManager, "BaseManager")

-- 9. Inicializa o sistema de convites
print("9. Inicializando InviteManager...")
safeRequire(script.Parent.InviteManager, "InviteManager")

-- 10. Inicializa o sistema de recursos
print("10. Inicializando ResourceManager...")
safeRequire(script.Parent.ResourceManager, "ResourceManager")

-- 11. Inicializa o sistema de coleta
print("11. Inicializando CollectionManager...")
safeRequire(script.Parent.CollectionManager, "CollectionManager")

-- 12. Inicializa o sistema de combate
print("12. Inicializando CombatManager...")
safeRequire(script.Parent.CombatManager, "CombatManager")

-- 13. Inicializa o sistema de depósito
print("13. Inicializando DepositManager...")
safeRequire(script.Parent.DepositManager, "DepositManager")

-- 14. Inicializa o sistema de ataque à base
print("14. Inicializando BaseAttackManager...")
safeRequire(script.Parent.BaseAttackManager, "BaseAttackManager")

-- 15. Inicializa o sistema de construção
print("15. Inicializando BuildingManager...")
safeRequire(script.Parent.BuildingManager, "BuildingManager")

-- 16. Inicializa o sistema de dash
print("16. Inicializando DashManager...")
safeRequire(script.Parent.DashManager, "DashManager")

print("🎉 === JOGO INICIALIZADO COM SUCESSO - FASE 4 === 🎉")
print("")
print("📋 INSTRUÇÕES DO JOGO:")
print("🏠 BASES:")
print("   • Toque em um ClaimPad amarelo para reivindicar uma base")
print("   • Use o botão CONVIDAR para formar duplas")
print("   • Cada base pode ter no máximo 2 jogadores")
print("")
print("⛏️ COLETA:")
print("   • Use a CollectorGun (azul) para coletar recursos")
print("   • Segure o botão esquerdo do mouse em um recurso")
print("   • Carregando recursos deixa você mais lento")
print("")
print("⚔️ COMBATE:")
print("   • Use a CombatGun (preta) para atacar outros jogadores")
print("   • Não é possível atacar membros da sua equipe")
print("   • Morrer reduz o tamanho da sua base")
print("")
print("📦 DEPÓSITO:")
print("   • Entre na barreira da sua base carregando recursos")
print("   • 60% vai para BaseSize, 40% para materiais de construção")
print("")
print("🏗️ CONSTRUÇÃO:")
print("   • Pressione 'B' dentro da sua barreira para construir")
print("   • 4 tipos de defesas disponíveis")
print("")
print("💥 ATAQUE À BASE:")
print("   • Use a CollectorGun para atacar bases inimigas")
print("   • Se a barreira tocar a torre, a base é destruída!")
print("")
print("💨 DASH:")
print("   • Use SHIFT para fazer dash em qualquer direção")
print("   • Dash na direção do movimento ou para frente")
print("   • Cooldown de 5 segundos entre usos")
print("   • Efeitos visuais e sonoros incluídos")
print("")
print("🎮 CONTROLES:")
print("   • WASD - Movimento")
print("   • Mouse - Mirar e atirar")
print("   • SHIFT - Dash (cooldown de 5 segundos)")
print("   • B - Menu de construção (dentro da barreira)")
print("")
print("🎮 DIVIRTA-SE!")