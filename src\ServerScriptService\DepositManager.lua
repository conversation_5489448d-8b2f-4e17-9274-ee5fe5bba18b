-- DepositManager.lua
-- Script responsável por gerenciar o depósito de recursos nas bases

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local depositResourceEvent = remoteEventsFolder:WaitForChild("DepositResource")

-- Carrega o BaseController
local BaseController = require(script.Parent.BaseController)

local DepositManager = {}

-- Configurações
local NORMAL_WALKSPEED = 16
local DEPOSIT_RANGE = 25 -- Distância da barreira para depositar

-- Cache de bases dos jogadores
local playerBases = {}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    if playerB<PERSON>[player] then
        return playerBases[player]
    end
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                playerBases[player] = base
                return base
            end
        end
    end
    
    return nil
end

-- Função para verificar se o jogador está na área de depósito
local function isInDepositArea(player, base)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return false end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    local barrierPosition = barrier.Position
    local distance = (playerPosition - barrierPosition).Magnitude
    
    return distance <= DEPOSIT_RANGE
end

-- Função para depositar recurso
local function depositResource(player)
    if not player.Character then return false end
    
    local carryingValue = player.Character:FindFirstChild("CarregandoRecurso")
    local resourceValue = player.Character:FindFirstChild("ResourceValue")
    
    if not carryingValue or not carryingValue.Value or not resourceValue then
        return false
    end
    
    local base = getPlayerBase(player)
    if not base then return false end
    
    if not isInDepositArea(player, base) then return false end
    
    local value = resourceValue.Value
    
    -- Remove estado de carregamento
    carryingValue.Value = false
    resourceValue:Destroy()
    
    -- Restaura velocidade
    if player.Character:FindFirstChild("Humanoid") then
        player.Character.Humanoid.WalkSpeed = NORMAL_WALKSPEED
    end
    
    -- Remove efeito visual
    local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
    if humanoidRootPart then
        local effect = humanoidRootPart:FindFirstChild("ResourceCarryingEffect")
        if effect then
            effect:Destroy()
        end
    end
    
    -- Divide o recurso entre BaseSize e BuildingMaterials
    local baseSizeValue = base:FindFirstChild("BaseSize")
    local buildingMaterialsValue = base:FindFirstChild("BuildingMaterials")
    
    if baseSizeValue and buildingMaterialsValue then
        -- Configuração da divisão de recursos
        local baseSizeIncrease = math.floor(value * 0.6) -- 60% para BaseSize
        local materialsIncrease = math.floor(value * 0.4) -- 40% para BuildingMaterials
        
        baseSizeValue.Value = baseSizeValue.Value + baseSizeIncrease
        buildingMaterialsValue.Value = buildingMaterialsValue.Value + materialsIncrease
        
        print(player.Name .. " depositou recurso no valor de " .. value)
        print("BaseSize: +" .. baseSizeIncrease .. " (Total: " .. baseSizeValue.Value .. ")")
        print("BuildingMaterials: +" .. materialsIncrease .. " (Total: " .. buildingMaterialsValue.Value .. ")")
        
        -- Atualiza visualmente o tamanho da base usando o BaseController
        BaseController.UpdateBaseSize(base)
    end
    
    -- Efeito visual de depósito
    local barrier = base:FindFirstChild("Barrier")
    if barrier then
        local originalTransparency = barrier.Transparency
        barrier.Transparency = 0.3
        
        spawn(function()
            wait(0.5)
            barrier.Transparency = originalTransparency
        end)
    end
    
    -- Notifica o cliente
    depositResourceEvent:FireClient(player, value, baseSizeValue and baseSizeValue.Value or 0, buildingMaterialsValue and buildingMaterialsValue.Value or 0)
    
    return true
end

-- Loop principal para verificar depósitos automáticos
local function checkAutoDeposits()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("CarregandoRecurso") then
            local carrying = player.Character.CarregandoRecurso.Value
            if carrying then
                local base = getPlayerBase(player)
                if base and isInDepositArea(player, base) then
                    depositResource(player)
                end
            end
        end
    end
end

-- Conecta o loop de verificação
RunService.Heartbeat:Connect(function()
    checkAutoDeposits()
end)

-- Manipula depósitos manuais (se necessário)
depositResourceEvent.OnServerEvent:Connect(function(player)
    depositResource(player)
end)

-- Limpa cache quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playerBases[player] = nil
end)

print("DepositManager inicializado com sucesso!")

return DepositManager