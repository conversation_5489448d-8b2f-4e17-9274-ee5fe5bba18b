-- BaseAttackManager.lua
-- Script responsável por gerenciar ataques às bases inimigas

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local startBaseAttackEvent = remoteEventsFolder:WaitForChild("StartBaseAttack")
local stopBaseAttackEvent = remoteEventsFolder:WaitForChild("StopBaseAttack")

-- Carrega o BaseController
local BaseController = require(script.Parent.BaseController)

local BaseAttackManager = {}

-- Configurações de ataque à base
local BASE_ATTACK_DAMAGE = 2 -- Dano por segundo à base
local BASE_ATTACK_RANGE = 150 -- Alcance máximo para atacar base
local UPDATE_INTERVAL = 0.5 -- Intervalo de atualização em segundos

-- Armazena ataques ativos
local activeBaseAttacks = {} -- [attacker] = {target = basePart, base = baseModel, startTime = tick()}

-- Cache de bases dos jogadores
local playerBases = {}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    if playerBases[player] then
        return playerBases[player]
    end
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                playerBases[player] = base
                return base
            end
        end
    end
    
    return nil
end

-- Função para encontrar a base que contém uma parte específica
local function findBaseFromPart(part)
    local parent = part.Parent
    while parent and parent ~= workspace do
        if parent.Name:match("Base_") then
            return parent
        end
        parent = parent.Parent
    end
    return nil
end

-- Função para verificar se um jogador pode atacar uma base
local function canAttackBase(attacker, targetBase)
    if not targetBase then return false end
    
    -- Verifica se a base tem dono
    local owner = targetBase:FindFirstChild("Owner")
    if not owner or not owner.Value then
        return false -- Não pode atacar base sem dono
    end
    
    -- Verifica se não é da mesma equipe
    local attackerBase = getPlayerBase(attacker)
    if attackerBase == targetBase then
        return false -- Não pode atacar própria base
    end
    
    -- Verifica distância
    if attacker.Character and attacker.Character:FindFirstChild("HumanoidRootPart") then
        local attackerPos = attacker.Character.HumanoidRootPart.Position
        local basePos = targetBase.PrimaryPart and targetBase.PrimaryPart.Position or Vector3.new(0, 0, 0)
        local distance = (attackerPos - basePos).Magnitude
        
        if distance > BASE_ATTACK_RANGE then
            return false -- Muito longe
        end
    end
    
    return true
end

-- Manipula início de ataque à base
startBaseAttackEvent.OnServerEvent:Connect(function(player, targetPart)
    if not targetPart or not targetPart.Parent then return end
    
    -- Verifica se é uma parte de base (Barrier ou BasePlatform)
    if targetPart.Name ~= "Barrier" and targetPart.Name ~= "BasePlatform" then
        return
    end
    
    -- Encontra a base
    local targetBase = findBaseFromPart(targetPart)
    if not targetBase then return end
    
    -- Verifica se pode atacar
    if not canAttackBase(player, targetBase) then
        return
    end
    
    -- Verifica se já está atacando
    if activeBaseAttacks[player] then
        return
    end
    
    -- Inicia o ataque
    activeBaseAttacks[player] = {
        target = targetPart,
        base = targetBase,
        startTime = tick()
    }
    
    print(player.Name .. " iniciou ataque à base " .. targetBase.Name)
end)

-- Manipula parada de ataque à base
stopBaseAttackEvent.OnServerEvent:Connect(function(player)
    if activeBaseAttacks[player] then
        local attack = activeBaseAttacks[player]
        print(player.Name .. " parou ataque à base " .. attack.base.Name)
        activeBaseAttacks[player] = nil
    end
end)

-- Função para processar ataques ativos
local function processBaseAttacks()
    for attacker, attack in pairs(activeBaseAttacks) do
        if not attacker.Parent then
            -- Jogador saiu do jogo
            activeBaseAttacks[attacker] = nil
        elseif not attack.base or not attack.base.Parent then
            -- Base foi destruída
            activeBaseAttacks[attacker] = nil
        elseif not canAttackBase(attacker, attack.base) then
            -- Não pode mais atacar (muito longe, etc.)
            activeBaseAttacks[attacker] = nil
        else
            -- Aplica dano à base
            local baseSizeValue = attack.base:FindFirstChild("BaseSize")
            if baseSizeValue then
                local damage = BASE_ATTACK_DAMAGE * UPDATE_INTERVAL
                baseSizeValue.Value = math.max(0, baseSizeValue.Value - damage)
                
                -- Atualiza visualmente
                BaseController.UpdateBaseSize(attack.base)
                
                -- Efeito visual no alvo
                local barrier = attack.base:FindFirstChild("Barrier")
                if barrier then
                    -- Pisca a barreira
                    local originalTransparency = barrier.Transparency
                    barrier.Transparency = 0.2
                    
                    spawn(function()
                        wait(0.1)
                        if barrier and barrier.Parent then
                            barrier.Transparency = originalTransparency
                        end
                    end)
                end
                
                print(attacker.Name .. " causou " .. damage .. " de dano à base " .. attack.base.Name .. " (Tamanho: " .. baseSizeValue.Value .. ")")
            end
        end
    end
end

-- Loop de processamento de ataques
spawn(function()
    while true do
        wait(UPDATE_INTERVAL)
        processBaseAttacks()
    end
end)

-- Limpa ataques quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    activeBaseAttacks[player] = nil
    playerBases[player] = nil
end)

-- Função para verificar se um jogador está atacando uma base
function BaseAttackManager.isAttackingBase(player)
    return activeBaseAttacks[player] ~= nil
end

-- Função para obter informações do ataque
function BaseAttackManager.getAttackInfo(player)
    return activeBaseAttacks[player]
end

print("BaseAttackManager inicializado com sucesso!")

return BaseAttackManager