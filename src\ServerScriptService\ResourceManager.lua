-- ResourceManager.lua
-- Script responsável por gerenciar recursos coletáveis no mapa

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

-- Carrega configurações
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local ResourceManager = {}

-- Configurações de recursos
local RESOURCE_TYPES = {
    {
        name = "Stone",
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Rock,
        size = Vector3.new(4, 4, 4),
        value = 10,
        spawnWeight = 0.4
    },
    {
        name = "Metal",
        color = BrickColor.new("Really black"),
        material = Enum.Material.Metal,
        size = Vector3.new(3, 3, 3),
        value = 15,
        spawnWeight = 0.3
    },
    {
        name = "Crystal",
        color = BrickColor.new("<PERSON>an"),
        material = Enum.Material.Neon,
        size = Vector3.new(2, 5, 2),
        value = 25,
        spawnWeight = 0.2
    },
    {
        name = "Wood",
        color = BrickColor.new("<PERSON>"),
        material = Enum.Material.Wood,
        size = Vector3.new(2, 6, 2),
        value = 8,
        spawnWeight = 0.1
    }
}

local SPAWN_AREAS = {
    {center = Vector3.new(0, 0, 0), radius = 300},
    {center = Vector3.new(150, 0, 150), radius = 100},
    {center = Vector3.new(-150, 0, -150), radius = 100},
    {center = Vector3.new(150, 0, -150), radius = 100},
    {center = Vector3.new(-150, 0, 150), radius = 100}
}

local MAX_RESOURCES = 50
local RESPAWN_TIME = 30 -- segundos
local activeResources = {}
local resourcesBeingCollected = {} -- [resource] = {collector, startTime}

-- Função para escolher tipo de recurso baseado no peso
local function chooseResourceType()
    local totalWeight = 0
    for _, resourceType in ipairs(RESOURCE_TYPES) do
        totalWeight = totalWeight + resourceType.spawnWeight
    end
    
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for _, resourceType in ipairs(RESOURCE_TYPES) do
        currentWeight = currentWeight + resourceType.spawnWeight
        if random <= currentWeight then
            return resourceType
        end
    end
    
    return RESOURCE_TYPES[1] -- fallback
end

-- Função para encontrar posição válida para spawn
local function findValidSpawnPosition(area)
    local attempts = 0
    local maxAttempts = 20
    
    while attempts < maxAttempts do
        local angle = math.random() * math.pi * 2
        local distance = math.random() * area.radius
        local x = area.center.X + math.cos(angle) * distance
        local z = area.center.Z + math.sin(angle) * distance
        
        -- Raycast para encontrar o chão
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        raycastParams.FilterDescendantsInstances = {}
        
        local raycastResult = workspace:Raycast(
            Vector3.new(x, 100, z),
            Vector3.new(0, -200, 0),
            raycastParams
        )
        
        if raycastResult then
            local y = raycastResult.Position.Y
            local position = Vector3.new(x, y + 2, z)
            
            -- Verifica se não há outros recursos muito próximos
            local tooClose = false
            for _, resource in ipairs(activeResources) do
                if resource and resource.Parent then
                    local distance = (resource.Position - position).Magnitude
                    if distance < 10 then
                        tooClose = true
                        break
                    end
                end
            end
            
            if not tooClose then
                return position
            end
        end
        
        attempts = attempts + 1
    end
    
    return nil
end

-- Cria um recurso
local function createResource(resourceType, position)
    local resource = Instance.new("Part")
    resource.Name = resourceType.name .. "Resource"
    resource.Size = resourceType.size
    resource.Position = position
    resource.BrickColor = resourceType.color
    resource.Material = resourceType.material
    resource.Shape = Enum.PartType.Block
    resource.Anchored = true
    resource.Parent = workspace
    
    -- Valores do recurso
    local originalSize = Instance.new("Vector3Value")
    originalSize.Name = "OriginalSize"
    originalSize.Value = resourceType.size
    originalSize.Parent = resource
    
    local resourceTypeValue = Instance.new("StringValue")
    resourceTypeValue.Name = "ResourceType"
    resourceTypeValue.Value = resourceType.name
    resourceTypeValue.Parent = resource
    
    local resourceValue = Instance.new("NumberValue")
    resourceValue.Name = "ResourceValue"
    resourceValue.Value = resourceType.value
    resourceValue.Parent = resource
    
    local currentSize = Instance.new("Vector3Value")
    currentSize.Name = "CurrentSize"
    currentSize.Value = resourceType.size
    currentSize.Parent = resource
    
    -- Adiciona à lista de recursos ativos
    table.insert(activeResources, resource)
    
    return resource
end

-- Spawna recursos iniciais
local function spawnInitialResources()
    for _ = 1, MAX_RESOURCES do
        local area = SPAWN_AREAS[math.random(1, #SPAWN_AREAS)]
        local position = findValidSpawnPosition(area)
        
        if position then
            local resourceType = chooseResourceType()
            createResource(resourceType, position)
        end
    end
    
    print("Spawned " .. #activeResources .. " initial resources")
end

-- Respawna recursos periodicamente
local function respawnResources()
    while true do
        wait(RESPAWN_TIME)

        -- Remove recursos destruídos da lista
        for i = #activeResources, 1, -1 do
            if not activeResources[i] or not activeResources[i].Parent then
                table.remove(activeResources, i)
            end
        end

        -- Spawna novos recursos se necessário
        local resourcesNeeded = MAX_RESOURCES - #activeResources
        for _ = 1, resourcesNeeded do
            -- Escolhe uma área aleatória para spawn
            local area = SPAWN_AREAS[math.random(1, #SPAWN_AREAS)]
            local position = findValidSpawnPosition(area)

            if position then
                local resourceType = chooseResourceType()
                createResource(resourceType, position)
                print("Recurso " .. resourceType.name .. " respawnado em posição aleatória: " .. tostring(position))
            else
                warn("Não foi possível encontrar posição válida para respawn na área " .. tostring(area.center))
            end
        end

        if resourcesNeeded > 0 then
            print("Respawned " .. resourcesNeeded .. " recursos em áreas aleatórias")
        end
    end
end

-- Função para iniciar coleta de um recurso
function ResourceManager.startCollecting(resource, collector)
    if not resource or not resource.Parent then return false end
    if resourcesBeingCollected[resource] then return false end
    
    resourcesBeingCollected[resource] = {
        collector = collector,
        startTime = tick()
    }
    
    return true
end

-- Função para parar coleta de um recurso
function ResourceManager.stopCollecting(resource)
    if not resource or not resourcesBeingCollected[resource] then return end
    
    resourcesBeingCollected[resource] = nil
end

-- Função para verificar se um recurso está sendo coletado
function ResourceManager.isBeingCollected(resource)
    return resourcesBeingCollected[resource] ~= nil
end

-- Função para obter o coletor de um recurso
function ResourceManager.getCollector(resource)
    local data = resourcesBeingCollected[resource]
    return data and data.collector or nil
end

-- Atualiza o tamanho dos recursos sendo coletados
local function updateResourceSizes()
    local SHRINK_RATE = 0.5 -- unidades por segundo
    local GROW_RATE = 1.0 -- unidades por segundo
    local MIN_SIZE_MULTIPLIER = 0.1
    
    for resource, data in pairs(resourcesBeingCollected) do
        if resource and resource.Parent then
            local originalSize = resource:FindFirstChild("OriginalSize")
            local currentSize = resource:FindFirstChild("CurrentSize")
            
            if originalSize and currentSize then
                local elapsed = tick() - data.startTime
                local shrinkAmount = SHRINK_RATE * elapsed
                
                -- Calcula novo tamanho
                local newSize = originalSize.Value - Vector3.new(shrinkAmount, shrinkAmount, shrinkAmount)
                local minSize = originalSize.Value * MIN_SIZE_MULTIPLIER
                
                -- Garante que não fique menor que o mínimo
                newSize = Vector3.new(
                    math.max(newSize.X, minSize.X),
                    math.max(newSize.Y, minSize.Y),
                    math.max(newSize.Z, minSize.Z)
                )
                
                currentSize.Value = newSize
                resource.Size = newSize
                
                -- Verifica se atingiu o tamanho mínimo (coleta completa)
                if newSize.X <= minSize.X then
                    ResourceManager.completeCollection(resource, data.collector)
                end
            end
        else
            -- Remove recurso inválido
            resourcesBeingCollected[resource] = nil
        end
    end
    
    -- Cresce recursos que não estão sendo coletados
    for _, resource in ipairs(activeResources) do
        if resource and resource.Parent and not resourcesBeingCollected[resource] then
            local originalSize = resource:FindFirstChild("OriginalSize")
            local currentSize = resource:FindFirstChild("CurrentSize")
            
            if originalSize and currentSize and currentSize.Value ~= originalSize.Value then
                local newSize = currentSize.Value + Vector3.new(GROW_RATE * 0.1, GROW_RATE * 0.1, GROW_RATE * 0.1)
                
                -- Não pode crescer além do tamanho original
                newSize = Vector3.new(
                    math.min(newSize.X, originalSize.Value.X),
                    math.min(newSize.Y, originalSize.Value.Y),
                    math.min(newSize.Z, originalSize.Value.Z)
                )
                
                currentSize.Value = newSize
                resource.Size = newSize
            end
        end
    end
end

-- Completa a coleta de um recurso
function ResourceManager.completeCollection(resource, collector)
    if not resource or not collector then return end
    
    local resourceValue = resource:FindFirstChild("ResourceValue")
    local value = resourceValue and resourceValue.Value or 10
    
    -- Remove da lista de coleta
    resourcesBeingCollected[resource] = nil
    
    -- Remove da lista de recursos ativos
    for i, activeResource in ipairs(activeResources) do
        if activeResource == resource then
            table.remove(activeResources, i)
            break
        end
    end
    
    -- Destroi o recurso
    resource:Destroy()
    
    -- Notifica o sistema de coleta
    local collectResourceEvent = ReplicatedStorage.RemoteEvents:FindFirstChild("CollectResource")
    if collectResourceEvent then
        collectResourceEvent:FireClient(collector, value)
    end
    
    print(collector.Name .. " coletou recurso no valor de " .. value)
end

-- Inicializa o sistema
spawn(function()
    spawnInitialResources()
    respawnResources()
end)

-- Loop de atualização
RunService.Heartbeat:Connect(updateResourceSizes)

print("ResourceManager inicializado com sucesso!")

return ResourceManager