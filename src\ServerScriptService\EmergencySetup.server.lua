-- EmergencySetup.server.lua
-- Script de emergência que cria tudo do zero se nada existir

local Workspace = game:GetService("Workspace")
local StarterPack = game:GetService("StarterPack")
local StarterPlayer = game:GetService("StarterPlayer")
local Lighting = game:GetService("Lighting")

-- Aguarda um pouco
wait(1)

-- Verifica se o jogo já foi inicializado
local baseplate = Workspace:FindFirstChild("Baseplate")
local hasTools = #StarterPack:GetChildren() > 0

-- Se não há nada, cria o básico imediatamente
if not baseplate or not hasTools then
    print("🚨 MODO EMERGÊNCIA ATIVADO - Criando conteúdo básico...")
    
    -- Cria baseplate se não existir
    if not baseplate then
        print("📦 Criando Baseplate...")
        local newBaseplate = Instance.new("Part")
        newBaseplate.Name = "Baseplate"
        newBaseplate.Size = Vector3.new(1000, 4, 1000)
        newBaseplate.Position = Vector3.new(0, -2, 0)
        newBaseplate.BrickColor = BrickColor.new("Bright green")
        newBaseplate.Material = Enum.Material.Grass
        newBaseplate.Anchored = true
        newBaseplate.Parent = Workspace
        
        -- Adiciona textura
        local texture = Instance.new("Texture")
        texture.Texture = "rbxasset://textures/terrain/grass.png"
        texture.Face = Enum.NormalId.Top
        texture.StudsPerTileU = 20
        texture.StudsPerTileV = 20
        texture.Parent = newBaseplate
    end
    
    -- Cria spawn central se não existir
    local centralSpawn = Workspace:FindFirstChild("CentralSpawn")
    if not centralSpawn then
        print("🏠 Criando CentralSpawn...")
        local spawn = Instance.new("SpawnLocation")
        spawn.Name = "CentralSpawn"
        spawn.Size = Vector3.new(12, 1, 12)
        spawn.Position = Vector3.new(0, 1, 0)
        spawn.BrickColor = BrickColor.new("Bright blue")
        spawn.Material = Enum.Material.Neon
        spawn.Anchored = true
        spawn.Parent = Workspace
    end
    
    -- Cria ferramentas básicas se não existirem
    if not hasTools then
        print("🔧 Criando ferramentas básicas...")
        
        -- CombatGun básica
        local combatGun = Instance.new("Tool")
        combatGun.Name = "CombatGun"
        combatGun.RequiresHandle = true
        combatGun.Parent = StarterPack
        
        local handle1 = Instance.new("Part")
        handle1.Name = "Handle"
        handle1.Size = Vector3.new(0.5, 1, 3)
        handle1.BrickColor = BrickColor.new("Really black")
        handle1.Material = Enum.Material.Metal
        handle1.Parent = combatGun
        
        -- CollectorGun básica
        local collectorGun = Instance.new("Tool")
        collectorGun.Name = "CollectorGun"
        collectorGun.RequiresHandle = true
        collectorGun.Parent = StarterPack
        
        local handle2 = Instance.new("Part")
        handle2.Name = "Handle"
        handle2.Size = Vector3.new(0.5, 1, 2.5)
        handle2.BrickColor = BrickColor.new("Bright blue")
        handle2.Material = Enum.Material.Plastic
        handle2.Parent = collectorGun
    end
    
    -- Configura StarterPlayer
    print("👤 Configurando StarterPlayer...")
    StarterPlayer.CharacterWalkSpeed = 16
    StarterPlayer.CharacterJumpPower = 50
    -- CharacterMaxHealth e RespawnTime não existem mais
    
    -- Configura iluminação
    print("💡 Configurando iluminação...")
    Lighting.Brightness = 2
    Lighting.Ambient = Color3.new(0.2, 0.2, 0.2)
    Lighting.TimeOfDay = "14:00:00"
    
    print("✅ MODO EMERGÊNCIA CONCLUÍDO!")
    print("🎮 Conteúdo básico criado. Execute AutoInit para funcionalidade completa.")
    
    -- Cria algumas bases básicas
    print("🏠 Criando bases básicas...")
    local basePositions = {
        Vector3.new(100, 5, 100),
        Vector3.new(-100, 5, 100),
        Vector3.new(100, 5, -100),
        Vector3.new(-100, 5, -100)
    }
    
    for i, pos in ipairs(basePositions) do
        -- ClaimPad
        local claimPad = Instance.new("Part")
        claimPad.Name = "ClaimPad_" .. i
        claimPad.Size = Vector3.new(8, 1, 8)
        claimPad.Position = pos
        claimPad.BrickColor = BrickColor.new("Bright yellow")
        claimPad.Material = Enum.Material.Neon
        claimPad.Anchored = true
        claimPad.Parent = Workspace
        
        -- Texto no ClaimPad
        local surfaceGui = Instance.new("SurfaceGui")
        surfaceGui.Face = Enum.NormalId.Top
        surfaceGui.Parent = claimPad
        
        local textLabel = Instance.new("TextLabel")
        textLabel.Size = UDim2.new(1, 0, 1, 0)
        textLabel.BackgroundTransparency = 1
        textLabel.Text = "CLAIM BASE " .. i
        textLabel.TextColor3 = Color3.new(0, 0, 0)
        textLabel.TextScaled = true
        textLabel.Font = Enum.Font.SourceSansBold
        textLabel.Parent = surfaceGui
        
        -- Torre central
        local tower = Instance.new("Part")
        tower.Name = "Tower_" .. i
        tower.Size = Vector3.new(2, 20, 2)
        tower.Position = pos + Vector3.new(0, 10, 0)
        tower.BrickColor = BrickColor.new("Dark stone grey")
        tower.Material = Enum.Material.Concrete
        tower.Anchored = true
        tower.Parent = Workspace
    end
    
    -- Cria alguns recursos básicos
    print("⛏️ Criando recursos básicos...")
    local resourcePositions = {
        Vector3.new(50, 2, 50),
        Vector3.new(-50, 2, 50),
        Vector3.new(50, 2, -50),
        Vector3.new(-50, 2, -50),
        Vector3.new(0, 2, 75),
        Vector3.new(0, 2, -75),
        Vector3.new(75, 2, 0),
        Vector3.new(-75, 2, 0)
    }
    
    for i, pos in ipairs(resourcePositions) do
        local resource = Instance.new("Part")
        resource.Name = "Resource_" .. i
        resource.Size = Vector3.new(4, 4, 4)
        resource.Position = pos
        resource.BrickColor = BrickColor.new("Dark stone grey")
        resource.Material = Enum.Material.Rock
        resource.Anchored = true
        resource.Parent = Workspace
        
        -- Adiciona valores básicos
        local originalSize = Instance.new("Vector3Value")
        originalSize.Name = "OriginalSize"
        originalSize.Value = Vector3.new(4, 4, 4)
        originalSize.Parent = resource
        
        local resourceType = Instance.new("StringValue")
        resourceType.Name = "ResourceType"
        resourceType.Value = "Stone"
        resourceType.Parent = resource
        
        local resourceValue = Instance.new("NumberValue")
        resourceValue.Name = "ResourceValue"
        resourceValue.Value = 10
        resourceValue.Parent = resource
    end
    
    print("🎉 SETUP EMERGENCIAL COMPLETO!")
    print("📋 Criado:")
    print("   • Baseplate de 1000x1000")
    print("   • Spawn central")
    print("   • 2 ferramentas básicas")
    print("   • 4 bases básicas")
    print("   • 8 recursos básicos")
    print("   • Configurações de jogador")
    print("")
    print("🔧 Para funcionalidade completa, execute o AutoInit!")
    
else
    print("✅ Jogo já inicializado - Modo emergência não necessário")
end