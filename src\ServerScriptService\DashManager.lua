-- DashManager.lua
-- Gerencia o sistema de dash no servidor

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitFor<PERSON>hild("RemoteEvents")
local dashEvent = remoteEventsFolder:WaitForChild("Dash")

local DashManager = {}

-- Configurações do dash
local DASH_FORCE = 50
local DASH_COOLDOWN = 5
local DASH_DURATION = 0.3

-- Controle de cooldowns por jogador
local playerCooldowns = {}

-- Função para aplicar o dash
local function applyDash(player, direction)
    if not player.Character or not player.Character:Find<PERSON><PERSON>t<PERSON>hild("HumanoidRootPart") then
        return false
    end
    
    local humanoidRootPart = player.Character.HumanoidRootPart
    local humanoid = player.Character:Find<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid")
    
    if not humanoid then return false end
    
    -- Verifica cooldown
    local currentTime = tick()
    local lastDash = playerCooldowns[player] or 0
    
    if currentTime - lastDash < DASH_COOLDOWN then
        return false
    end
    
    -- Atualiza cooldown
    playerCooldowns[player] = currentTime
    
    -- Cria BodyVelocity para o dash
    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.MaxForce = Vector3.new(4000, 0, 4000) -- Só aplica força horizontal
    bodyVelocity.Velocity = direction * DASH_FORCE
    bodyVelocity.Parent = humanoidRootPart
    
    -- Remove o BodyVelocity após a duração do dash
    game:GetService("Debris"):AddItem(bodyVelocity, DASH_DURATION)
    
    print(player.Name .. " fez dash na direção " .. tostring(direction))
    return true
end

-- Conecta o evento de dash
dashEvent.OnServerEvent:Connect(function(player, direction)
    -- Valida a direção
    if typeof(direction) ~= "Vector3" then
        return
    end
    
    -- Normaliza e remove componente Y
    direction = Vector3.new(direction.X, 0, direction.Z).Unit
    
    -- Aplica o dash
    applyDash(player, direction)
end)

-- Limpa cooldowns quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playerCooldowns[player] = nil
end)

print("DashManager inicializado com sucesso!")

return DashManager