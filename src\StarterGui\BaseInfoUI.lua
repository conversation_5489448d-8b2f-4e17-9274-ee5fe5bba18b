-- BaseInfoUI.lua
-- Script local para mostrar informações sobre a base do jogador

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui")

local BaseInfoUI = {}

-- Variáveis da UI
local infoGui
local baseInfoFrame
local ownerLabel
local partnerLabel
local baseSizeLabel
local statusLabel

-- Cria a interface de informações da base
local function createBaseInfoUI()
    -- ScreenGui para informações da base
    infoGui = Instance.new("ScreenGui")
    infoGui.Name = "BaseInfoUI"
    infoGui.ResetOnSpawn = false
    infoGui.Parent = playerGui
    
    -- Frame principal
    baseInfoFrame = Instance.new("Frame")
    baseInfoFrame.Name = "BaseInfoFrame"
    baseInfoFrame.Size = UDim2.new(0, 250, 0, 120)
    baseInfoFrame.Position = UDim2.new(0, 20, 1, -140)
    baseInfoFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    baseInfoFrame.BorderSizePixel = 0
    baseInfoFrame.Visible = false
    baseInfoFrame.Parent = infoGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 25)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "INFORMAÇÕES DA BASE"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = baseInfoFrame
    
    -- Status
    statusLabel = Instance.new("TextLabel")
    statusLabel.Name = "Status"
    statusLabel.Size = UDim2.new(1, -10, 0, 20)
    statusLabel.Position = UDim2.new(0, 5, 0, 30)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = "Status: Sem Base"
    statusLabel.TextColor3 = Color3.new(1, 0.5, 0.5)
    statusLabel.TextScaled = true
    statusLabel.Font = Enum.Font.SourceSans
    statusLabel.TextXAlignment = Enum.TextXAlignment.Left
    statusLabel.Parent = baseInfoFrame
    
    -- Dono
    ownerLabel = Instance.new("TextLabel")
    ownerLabel.Name = "Owner"
    ownerLabel.Size = UDim2.new(1, -10, 0, 20)
    ownerLabel.Position = UDim2.new(0, 5, 0, 50)
    ownerLabel.BackgroundTransparency = 1
    ownerLabel.Text = "Dono: -"
    ownerLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    ownerLabel.TextScaled = true
    ownerLabel.Font = Enum.Font.SourceSans
    ownerLabel.TextXAlignment = Enum.TextXAlignment.Left
    ownerLabel.Parent = baseInfoFrame
    
    -- Parceiro
    partnerLabel = Instance.new("TextLabel")
    partnerLabel.Name = "Partner"
    partnerLabel.Size = UDim2.new(1, -10, 0, 20)
    partnerLabel.Position = UDim2.new(0, 5, 0, 70)
    partnerLabel.BackgroundTransparency = 1
    partnerLabel.Text = "Parceiro: -"
    partnerLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    partnerLabel.TextScaled = true
    partnerLabel.Font = Enum.Font.SourceSans
    partnerLabel.TextXAlignment = Enum.TextXAlignment.Left
    partnerLabel.Parent = baseInfoFrame
    
    -- Tamanho da base
    baseSizeLabel = Instance.new("TextLabel")
    baseSizeLabel.Name = "BaseSize"
    baseSizeLabel.Size = UDim2.new(1, -10, 0, 20)
    baseSizeLabel.Position = UDim2.new(0, 5, 0, 90)
    baseSizeLabel.BackgroundTransparency = 1
    baseSizeLabel.Text = "Tamanho: -"
    baseSizeLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    baseSizeLabel.TextScaled = true
    baseSizeLabel.Font = Enum.Font.SourceSans
    baseSizeLabel.TextXAlignment = Enum.TextXAlignment.Left
    baseSizeLabel.Parent = baseInfoFrame
end

-- Encontra a base do jogador
local function findPlayerBase()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                return base
            end
        end
    end
    return nil
end

-- Atualiza as informações da base
local function updateBaseInfo()
    local base = findPlayerBase()
    
    if base then
        baseInfoFrame.Visible = true
        
        local owner = base:FindFirstChild("Owner")
        local partner = base:FindFirstChild("Partner")
        local baseSize = base:FindFirstChild("BaseSize")
        
        -- Determina o status do jogador
        local playerStatus = ""
        if owner and owner.Value == player then
            playerStatus = "Dono da Base"
            statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
        elseif partner and partner.Value == player then
            playerStatus = "Parceiro"
            statusLabel.TextColor3 = Color3.new(0.2, 0.6, 0.8)
        end
        
        statusLabel.Text = "Status: " .. playerStatus
        ownerLabel.Text = "Dono: " .. (owner and owner.Value and owner.Value.Name or "-")
        partnerLabel.Text = "Parceiro: " .. (partner and partner.Value and partner.Value.Name or "-")
        baseSizeLabel.Text = "Tamanho: " .. (baseSize and baseSize.Value or "-")
        
    else
        baseInfoFrame.Visible = false
    end
end

-- Atualiza a cada frame
local connection
connection = RunService.Heartbeat:Connect(updateBaseInfo)

-- Limpa quando o jogador sai
Players.PlayerRemoving:Connect(function(leavingPlayer)
    if leavingPlayer == player then
        connection:Disconnect()
    end
end)

-- Inicializa a UI
createBaseInfoUI()

print("BaseInfoUI inicializada com sucesso!")

return BaseInfoUI