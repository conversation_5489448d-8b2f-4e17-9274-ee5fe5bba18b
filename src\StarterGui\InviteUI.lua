-- InviteUI.lua
-- Script local para criar e gerenciar a interface de convites

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- <PERSON>guarda os RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local sendInviteEvent = remoteEventsFolder:WaitForChild("SendInvite")
local respondInviteEvent = remoteEventsFolder:WaitForChild("RespondInvite")
local updateInviteUIEvent = remoteEventsFolder:WaitForChild("UpdateInviteUI")

local InviteUI = {}

-- Variáveis da UI
local mainGui
local inviteFrame
local playerListFrame
local notificationFrame
local currentInviteFrame

-- Cria a interface principal
local function createMainUI()
    -- ScreenGui principal
    mainGui = Instance.new("ScreenGui")
    mainGui.Name = "InviteUI"
    mainGui.ResetOnSpawn = false
    mainGui.Parent = playerGui
    
    -- Frame principal para convites
    inviteFrame = Instance.new("Frame")
    inviteFrame.Name = "InviteFrame"
    inviteFrame.Size = UDim2.new(0, 300, 0, 400)
    inviteFrame.Position = UDim2.new(1, -320, 0.5, -200)
    inviteFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    inviteFrame.BorderSizePixel = 0
    inviteFrame.Visible = false
    inviteFrame.Parent = mainGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "CONVIDAR PARCEIRO"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = inviteFrame
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = inviteFrame
    
    -- Lista de jogadores
    playerListFrame = Instance.new("ScrollingFrame")
    playerListFrame.Name = "PlayerList"
    playerListFrame.Size = UDim2.new(1, -20, 1, -60)
    playerListFrame.Position = UDim2.new(0, 10, 0, 50)
    playerListFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    playerListFrame.BorderSizePixel = 0
    playerListFrame.ScrollBarThickness = 8
    playerListFrame.Parent = inviteFrame
    
    -- Botão para abrir o menu de convites
    local inviteButton = Instance.new("TextButton")
    inviteButton.Name = "InviteButton"
    inviteButton.Size = UDim2.new(0, 120, 0, 40)
    inviteButton.Position = UDim2.new(0, 20, 0, 20)
    inviteButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.2)
    inviteButton.BorderSizePixel = 0
    inviteButton.Text = "CONVIDAR"
    inviteButton.TextColor3 = Color3.new(1, 1, 1)
    inviteButton.TextScaled = true
    inviteButton.Font = Enum.Font.SourceSansBold
    inviteButton.Parent = mainGui
    
    -- Eventos dos botões
    inviteButton.MouseButton1Click:Connect(function()
        inviteFrame.Visible = not inviteFrame.Visible
        if inviteFrame.Visible then
            updatePlayerList()
        end
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        inviteFrame.Visible = false
    end)
end

-- Atualiza a lista de jogadores
function updatePlayerList()
    -- Limpa a lista atual
    for _, child in ipairs(playerListFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end
    
    local yOffset = 0
    
    for _, otherPlayer in ipairs(Players:GetPlayers()) do
        if otherPlayer ~= player then
            local playerFrame = Instance.new("Frame")
            playerFrame.Name = otherPlayer.Name
            playerFrame.Size = UDim2.new(1, -10, 0, 50)
            playerFrame.Position = UDim2.new(0, 5, 0, yOffset)
            playerFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
            playerFrame.BorderSizePixel = 0
            playerFrame.Parent = playerListFrame
            
            local nameLabel = Instance.new("TextLabel")
            nameLabel.Size = UDim2.new(0.6, 0, 1, 0)
            nameLabel.Position = UDim2.new(0, 10, 0, 0)
            nameLabel.BackgroundTransparency = 1
            nameLabel.Text = otherPlayer.Name
            nameLabel.TextColor3 = Color3.new(1, 1, 1)
            nameLabel.TextScaled = true
            nameLabel.Font = Enum.Font.SourceSans
            nameLabel.TextXAlignment = Enum.TextXAlignment.Left
            nameLabel.Parent = playerFrame
            
            local invitePlayerButton = Instance.new("TextButton")
            invitePlayerButton.Size = UDim2.new(0.35, 0, 0.8, 0)
            invitePlayerButton.Position = UDim2.new(0.6, 0, 0.1, 0)
            invitePlayerButton.BackgroundColor3 = Color3.new(0.3, 0.7, 0.3)
            invitePlayerButton.BorderSizePixel = 0
            invitePlayerButton.Text = "CONVIDAR"
            invitePlayerButton.TextColor3 = Color3.new(1, 1, 1)
            invitePlayerButton.TextScaled = true
            invitePlayerButton.Font = Enum.Font.SourceSansBold
            invitePlayerButton.Parent = playerFrame
            
            invitePlayerButton.MouseButton1Click:Connect(function()
                sendInviteEvent:FireServer(otherPlayer.Name)
                inviteFrame.Visible = false
            end)
            
            yOffset = yOffset + 55
        end
    end
    
    playerListFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset)
end

-- Cria frame de notificação
local function createNotificationFrame()
    notificationFrame = Instance.new("Frame")
    notificationFrame.Name = "NotificationFrame"
    notificationFrame.Size = UDim2.new(0, 300, 0, 80)
    notificationFrame.Position = UDim2.new(0.5, -150, 0, -100)
    notificationFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    notificationFrame.BorderSizePixel = 0
    notificationFrame.Visible = false
    notificationFrame.Parent = mainGui
    
    local notificationText = Instance.new("TextLabel")
    notificationText.Name = "NotificationText"
    notificationText.Size = UDim2.new(1, -20, 1, -20)
    notificationText.Position = UDim2.new(0, 10, 0, 10)
    notificationText.BackgroundTransparency = 1
    notificationText.Text = ""
    notificationText.TextColor3 = Color3.new(1, 1, 1)
    notificationText.TextScaled = true
    notificationText.Font = Enum.Font.SourceSans
    notificationText.TextWrapped = true
    notificationText.Parent = notificationFrame
end

-- Cria frame de convite recebido
local function createInviteReceivedFrame()
    currentInviteFrame = Instance.new("Frame")
    currentInviteFrame.Name = "CurrentInviteFrame"
    currentInviteFrame.Size = UDim2.new(0, 350, 0, 150)
    currentInviteFrame.Position = UDim2.new(0.5, -175, 0.5, -75)
    currentInviteFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    currentInviteFrame.BorderSizePixel = 2
    currentInviteFrame.BorderColor3 = Color3.new(0.3, 0.7, 0.3)
    currentInviteFrame.Visible = false
    currentInviteFrame.Parent = mainGui
    
    local inviteText = Instance.new("TextLabel")
    inviteText.Name = "InviteText"
    inviteText.Size = UDim2.new(1, -20, 0.6, 0)
    inviteText.Position = UDim2.new(0, 10, 0, 10)
    inviteText.BackgroundTransparency = 1
    inviteText.Text = ""
    inviteText.TextColor3 = Color3.new(1, 1, 1)
    inviteText.TextScaled = true
    inviteText.Font = Enum.Font.SourceSans
    inviteText.TextWrapped = true
    inviteText.Parent = currentInviteFrame
    
    local acceptButton = Instance.new("TextButton")
    acceptButton.Name = "AcceptButton"
    acceptButton.Size = UDim2.new(0.4, 0, 0.25, 0)
    acceptButton.Position = UDim2.new(0.05, 0, 0.7, 0)
    acceptButton.BackgroundColor3 = Color3.new(0.2, 0.7, 0.2)
    acceptButton.BorderSizePixel = 0
    acceptButton.Text = "ACEITAR"
    acceptButton.TextColor3 = Color3.new(1, 1, 1)
    acceptButton.TextScaled = true
    acceptButton.Font = Enum.Font.SourceSansBold
    acceptButton.Parent = currentInviteFrame
    
    local declineButton = Instance.new("TextButton")
    declineButton.Name = "DeclineButton"
    declineButton.Size = UDim2.new(0.4, 0, 0.25, 0)
    declineButton.Position = UDim2.new(0.55, 0, 0.7, 0)
    declineButton.BackgroundColor3 = Color3.new(0.7, 0.2, 0.2)
    declineButton.BorderSizePixel = 0
    declineButton.Text = "RECUSAR"
    declineButton.TextColor3 = Color3.new(1, 1, 1)
    declineButton.TextScaled = true
    declineButton.Font = Enum.Font.SourceSansBold
    declineButton.Parent = currentInviteFrame
    
    acceptButton.MouseButton1Click:Connect(function()
        respondInviteEvent:FireServer("accept")
        currentInviteFrame.Visible = false
    end)
    
    declineButton.MouseButton1Click:Connect(function()
        respondInviteEvent:FireServer("decline")
        currentInviteFrame.Visible = false
    end)
end

-- Mostra notificação
local function showNotification(message, color)
    local notificationText = notificationFrame:FindFirstChild("NotificationText")
    if notificationText then
        notificationText.Text = message
        notificationFrame.BackgroundColor3 = color or Color3.new(0.1, 0.1, 0.1)
        notificationFrame.Visible = true
        notificationFrame.Position = UDim2.new(0.5, -150, 0, -100)
        
        -- Anima a entrada
        local tweenIn = TweenService:Create(
            notificationFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
            {Position = UDim2.new(0.5, -150, 0, 20)}
        )
        tweenIn:Play()
        
        -- Remove após 3 segundos
        wait(3)
        local tweenOut = TweenService:Create(
            notificationFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
            {Position = UDim2.new(0.5, -150, 0, -100)}
        )
        tweenOut:Play()
        tweenOut.Completed:Connect(function()
            notificationFrame.Visible = false
        end)
    end
end

-- Manipula atualizações da UI
updateInviteUIEvent.OnClientEvent:Connect(function(eventType, data)
    if eventType == "error" then
        showNotification("Erro: " .. data, Color3.new(0.7, 0.2, 0.2))
        
    elseif eventType == "sent" then
        showNotification("Convite enviado para " .. data, Color3.new(0.2, 0.7, 0.2))
        
    elseif eventType == "received" then
        local inviteText = currentInviteFrame:FindFirstChild("InviteText")
        if inviteText then
            inviteText.Text = data .. " convidou você para ser seu parceiro!"
            currentInviteFrame.Visible = true
        end
        
    elseif eventType == "accepted" then
        showNotification("Você se juntou à equipe de " .. data, Color3.new(0.2, 0.7, 0.2))
        
    elseif eventType == "declined" then
        showNotification("Você recusou o convite de " .. data, Color3.new(0.7, 0.7, 0.2))
        
    elseif eventType == "partner_joined" then
        showNotification(data .. " se juntou à sua equipe!", Color3.new(0.2, 0.7, 0.2))
        
    elseif eventType == "invite_declined" then
        showNotification(data .. " recusou seu convite", Color3.new(0.7, 0.7, 0.2))
    end
end)

-- Inicializa a UI
createMainUI()
createNotificationFrame()
createInviteReceivedFrame()

print("InviteUI inicializada com sucesso!")

return InviteUI