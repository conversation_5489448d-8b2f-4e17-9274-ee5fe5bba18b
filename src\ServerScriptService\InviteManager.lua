-- InviteManager.lua
-- Script responsável por gerenciar o sistema de convites para duplas

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda os RemoteEvents serem criados
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local sendInviteEvent = remoteEventsFolder:WaitForChild("SendInvite")
local respondInviteEvent = remoteEventsFolder:WaitForChild("RespondInvite")
local updateInviteUIEvent = remoteEventsFolder:WaitForChild("UpdateInviteUI")

local InviteManager = {}

-- Armazena convites pendentes
local pendingInvites = {} -- [targetPlayer] = {sender = player, base = base}
local playerBases = {} -- Cache das bases dos jogadores

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    if playerB<PERSON>[player] then
        return playerB<PERSON>[player]
    end
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            if owner and owner.Value == player then
                playerBases[player] = base
                return base
            end
        end
    end
    
    return nil
end

-- Função para verificar se um jogador pode enviar convites
local function canSendInvite(player)
    local base = getPlayerBase(player)
    if not base then return false, "Você precisa ter uma base para enviar convites" end
    
    local partner = base:FindFirstChild("Partner")
    if partner and partner.Value then
        return false, "Você já tem um parceiro"
    end
    
    return true, ""
end

-- Função para verificar se um jogador pode receber convites
local function canReceiveInvite(player)
    if getPlayerBase(player) then
        return false, "O jogador já possui uma base"
    end
    
    if player.RespawnLocation then
        return false, "O jogador já faz parte de uma equipe"
    end
    
    if pendingInvites[player] then
        return false, "O jogador já tem um convite pendente"
    end
    
    return true, ""
end

-- Função para atribuir cor ao parceiro
local function assignColorToPartner(base, partner)
    if not partner.Character then return end
    
    local basePlatform = base:FindFirstChild("BasePlatform")
    if not basePlatform then return end
    
    local teamColor = basePlatform.BrickColor
    
    -- Muda a cor do uniforme do parceiro
    for _, part in ipairs(partner.Character:GetChildren()) do
        if part:IsA("BasePart") and part.Name ~= "Head" then
            part.BrickColor = teamColor
        end
    end
end

-- Manipula o envio de convites
sendInviteEvent.OnServerEvent:Connect(function(sender, targetPlayerName)
    local targetPlayer = Players:FindFirstChild(targetPlayerName)
    if not targetPlayer then
        updateInviteUIEvent:FireClient(sender, "error", "Jogador não encontrado")
        return
    end
    
    if sender == targetPlayer then
        updateInviteUIEvent:FireClient(sender, "error", "Você não pode convidar a si mesmo")
        return
    end
    
    -- Verifica se o remetente pode enviar convites
    local canSend, sendError = canSendInvite(sender)
    if not canSend then
        updateInviteUIEvent:FireClient(sender, "error", sendError)
        return
    end
    
    -- Verifica se o alvo pode receber convites
    local canReceive, receiveError = canReceiveInvite(targetPlayer)
    if not canReceive then
        updateInviteUIEvent:FireClient(sender, "error", receiveError)
        return
    end
    
    -- Cria o convite
    local base = getPlayerBase(sender)
    pendingInvites[targetPlayer] = {
        sender = sender,
        base = base
    }
    
    -- Notifica ambos os jogadores
    updateInviteUIEvent:FireClient(sender, "sent", targetPlayer.Name)
    updateInviteUIEvent:FireClient(targetPlayer, "received", sender.Name)
    
    print(sender.Name .. " enviou convite para " .. targetPlayer.Name)
end)

-- Manipula as respostas aos convites
respondInviteEvent.OnServerEvent:Connect(function(player, response)
    local invite = pendingInvites[player]
    if not invite then
        updateInviteUIEvent:FireClient(player, "error", "Nenhum convite pendente")
        return
    end
    
    local sender = invite.sender
    local base = invite.base
    
    -- Remove o convite da lista
    pendingInvites[player] = nil
    
    if response == "accept" then
        -- Verifica se a base ainda é válida
        if not base or not base.Parent then
            updateInviteUIEvent:FireClient(player, "error", "Base não é mais válida")
            updateInviteUIEvent:FireClient(sender, "error", "Convite expirou")
            return
        end
        
        local owner = base:FindFirstChild("Owner")
        local partner = base:FindFirstChild("Partner")
        local spawnLocation = base:FindFirstChild("SpawnLocation")
        
        if not owner or owner.Value ~= sender then
            updateInviteUIEvent:FireClient(player, "error", "O remetente não é mais dono da base")
            updateInviteUIEvent:FireClient(sender, "error", "Você não é mais dono desta base")
            return
        end
        
        if partner.Value then
            updateInviteUIEvent:FireClient(player, "error", "A base já tem um parceiro")
            updateInviteUIEvent:FireClient(sender, "error", "Você já tem um parceiro")
            return
        end
        
        -- Aceita o convite
        partner.Value = player
        playerBases[player] = base
        player.RespawnLocation = spawnLocation
        
        -- Atribui a cor da equipe ao parceiro
        assignColorToPartner(base, player)
        
        -- Notifica ambos os jogadores
        updateInviteUIEvent:FireClient(player, "accepted", sender.Name)
        updateInviteUIEvent:FireClient(sender, "partner_joined", player.Name)
        
        print(player.Name .. " aceitou o convite de " .. sender.Name)
        
    else -- response == "decline"
        -- Notifica ambos os jogadores
        updateInviteUIEvent:FireClient(player, "declined", sender.Name)
        updateInviteUIEvent:FireClient(sender, "invite_declined", player.Name)
        
        print(player.Name .. " recusou o convite de " .. sender.Name)
    end
end)

-- Limpa convites quando jogadores saem
Players.PlayerRemoving:Connect(function(player)
    -- Remove convites pendentes para este jogador
    pendingInvites[player] = nil
    
    -- Remove convites enviados por este jogador
    for targetPlayer, invite in pairs(pendingInvites) do
        if invite.sender == player then
            pendingInvites[targetPlayer] = nil
            updateInviteUIEvent:FireClient(targetPlayer, "error", "O remetente saiu do jogo")
        end
    end
    
    -- Remove do cache de bases
    playerBases[player] = nil
end)

print("InviteManager inicializado com sucesso!")

return InviteManager