-- ForceCreateStarterPack.lua
-- Script para forçar a criação do StarterPack se ele não existir

local StarterPack = game:GetService("StarterPack")

local function forceCreateStarterPack()
    -- Verifica se o StarterPack existe, se não, aguarda
    if not StarterPack then
        warn("StarterPack não encontrado! Aguardando...")
        StarterPack = game:GetService("StarterPack")
    end
    
    print("StarterPack encontrado: " .. StarterPack.Name)
    
    -- Remove todas as ferramentas existentes para evitar duplicatas
    for _, tool in ipairs(StarterPack:GetChildren()) do
        if tool:IsA("Tool") then
            tool:Destroy()
        end
    end
    
    print("StarterPack limpo e pronto para receber ferramentas")
    return true
end

-- Executa a função
return forceCreateStarterPack()