-- GameplayUI.lua
-- Interface para mostrar informações de gameplay (recursos, saúde, etc.)

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Aguarda RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local collectResourceEvent = remoteEventsFolder:WaitForChild("CollectResource")
local playerDamagedEvent = remoteEventsFolder:WaitForChild("PlayerDamaged")
local depositResourceEvent = remoteEventsFolder:WaitForChild("DepositResource")

local GameplayUI = {}

-- Variáveis da UI
local mainGui
local healthBar
local resourceStatus
local baseAttackStatus
local notificationFrame
local damageIndicator

-- Cria a interface principal
local function createGameplayUI()
    -- ScreenGui principal
    mainGui = Instance.new("ScreenGui")
    mainGui.Name = "GameplayUI"
    mainGui.ResetOnSpawn = false
    mainGui.Parent = playerGui
    
    -- Frame principal (HUD)
    local hudFrame = Instance.new("Frame")
    hudFrame.Name = "HUDFrame"
    hudFrame.Size = UDim2.new(0, 300, 0, 100)
    hudFrame.Position = UDim2.new(0, 20, 0, 80)
    hudFrame.BackgroundTransparency = 1
    hudFrame.Parent = mainGui
    
    -- Barra de vida
    local healthFrame = Instance.new("Frame")
    healthFrame.Name = "HealthFrame"
    healthFrame.Size = UDim2.new(1, 0, 0, 25)
    healthFrame.Position = UDim2.new(0, 0, 0, 0)
    healthFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    healthFrame.BorderSizePixel = 1
    healthFrame.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    healthFrame.Parent = hudFrame
    
    healthBar = Instance.new("Frame")
    healthBar.Name = "HealthBar"
    healthBar.Size = UDim2.new(1, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthFrame
    
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Name = "HealthLabel"
    healthLabel.Size = UDim2.new(1, 0, 1, 0)
    healthLabel.Position = UDim2.new(0, 0, 0, 0)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "VIDA: 100/100"
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.SourceSansBold
    healthLabel.Parent = healthFrame
    
    -- Status de recurso
    resourceStatus = Instance.new("Frame")
    resourceStatus.Name = "ResourceStatus"
    resourceStatus.Size = UDim2.new(1, 0, 0, 25)
    resourceStatus.Position = UDim2.new(0, 0, 0, 35)
    resourceStatus.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    resourceStatus.BorderSizePixel = 1
    resourceStatus.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    resourceStatus.Visible = false
    resourceStatus.Parent = hudFrame
    
    local resourceLabel = Instance.new("TextLabel")
    resourceLabel.Name = "ResourceLabel"
    resourceLabel.Size = UDim2.new(1, 0, 1, 0)
    resourceLabel.Position = UDim2.new(0, 0, 0, 0)
    resourceLabel.BackgroundTransparency = 1
    resourceLabel.Text = "CARREGANDO RECURSO"
    resourceLabel.TextColor3 = Color3.new(1, 1, 0)
    resourceLabel.TextScaled = true
    resourceLabel.Font = Enum.Font.SourceSansBold
    resourceLabel.Parent = resourceStatus
    
    -- Status de ataque à base
    baseAttackStatus = Instance.new("Frame")
    baseAttackStatus.Name = "BaseAttackStatus"
    baseAttackStatus.Size = UDim2.new(1, 0, 0, 25)
    baseAttackStatus.Position = UDim2.new(0, 0, 0, 70)
    baseAttackStatus.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    baseAttackStatus.BorderSizePixel = 1
    baseAttackStatus.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
    baseAttackStatus.Visible = false
    baseAttackStatus.Parent = hudFrame
    
    local attackLabel = Instance.new("TextLabel")
    attackLabel.Name = "AttackLabel"
    attackLabel.Size = UDim2.new(1, 0, 1, 0)
    attackLabel.Position = UDim2.new(0, 0, 0, 0)
    attackLabel.BackgroundTransparency = 1
    attackLabel.Text = "ATACANDO BASE INIMIGA"
    attackLabel.TextColor3 = Color3.new(1, 0, 0)
    attackLabel.TextScaled = true
    attackLabel.Font = Enum.Font.SourceSansBold
    attackLabel.Parent = baseAttackStatus
    
    -- Indicador de dano
    damageIndicator = Instance.new("Frame")
    damageIndicator.Name = "DamageIndicator"
    damageIndicator.Size = UDim2.new(1, 0, 1, 0)
    damageIndicator.Position = UDim2.new(0, 0, 0, 0)
    damageIndicator.BackgroundColor3 = Color3.new(1, 0, 0)
    damageIndicator.Transparency = 1
    damageIndicator.BorderSizePixel = 0
    damageIndicator.Parent = mainGui
    
    -- Frame de notificações
    notificationFrame = Instance.new("Frame")
    notificationFrame.Name = "NotificationFrame"
    notificationFrame.Size = UDim2.new(0, 400, 0, 60)
    notificationFrame.Position = UDim2.new(0.5, -200, 0.8, 0)
    notificationFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    notificationFrame.BorderSizePixel = 0
    notificationFrame.Visible = false
    notificationFrame.Parent = mainGui
    
    local notificationText = Instance.new("TextLabel")
    notificationText.Name = "NotificationText"
    notificationText.Size = UDim2.new(1, -20, 1, -20)
    notificationText.Position = UDim2.new(0, 10, 0, 10)
    notificationText.BackgroundTransparency = 1
    notificationText.Text = ""
    notificationText.TextColor3 = Color3.new(1, 1, 1)
    notificationText.TextScaled = true
    notificationText.Font = Enum.Font.SourceSans
    notificationText.TextWrapped = true
    notificationText.Parent = notificationFrame
end

-- Atualiza a barra de vida
local function updateHealthBar()
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        local healthPercent = humanoid.Health / humanoid.MaxHealth
        
        healthBar.Size = UDim2.new(healthPercent, 0, 1, 0)
        
        local healthLabel = healthBar.Parent:FindFirstChild("HealthLabel")
        if healthLabel then
            healthLabel.Text = "VIDA: " .. math.floor(humanoid.Health) .. "/" .. math.floor(humanoid.MaxHealth)
        end
        
        -- Muda cor baseada na vida
        if healthPercent > 0.6 then
            healthBar.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
        elseif healthPercent > 0.3 then
            healthBar.BackgroundColor3 = Color3.new(0.8, 0.8, 0.2)
        else
            healthBar.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
        end
    end
end

-- Atualiza status de recurso
local function updateResourceStatus()
    if player.Character then
        local carryingValue = player.Character:FindFirstChild("CarregandoRecurso")
        local resourceValue = player.Character:FindFirstChild("ResourceValue")
        
        if carryingValue and carryingValue.Value and resourceValue then
            resourceStatus.Visible = true
            local resourceLabel = resourceStatus:FindFirstChild("ResourceLabel")
            if resourceLabel then
                resourceLabel.Text = "CARREGANDO RECURSO (Valor: " .. resourceValue.Value .. ")"
            end
        else
            resourceStatus.Visible = false
        end
    else
        resourceStatus.Visible = false
    end
end

-- Mostra notificação
local function showNotification(message, color, duration)
    local notificationText = notificationFrame:FindFirstChild("NotificationText")
    if notificationText then
        notificationText.Text = message
        notificationFrame.BackgroundColor3 = color or Color3.new(0.1, 0.1, 0.1)
        notificationFrame.Visible = true
        
        -- Anima a entrada
        notificationFrame.Position = UDim2.new(0.5, -200, 1, 0)
        local tweenIn = TweenService:Create(
            notificationFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
            {Position = UDim2.new(0.5, -200, 0.8, 0)}
        )
        tweenIn:Play()
        
        -- Remove após o tempo especificado
        spawn(function()
            wait(duration or 3)
            local tweenOut = TweenService:Create(
                notificationFrame,
                TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
                {Position = UDim2.new(0.5, -200, 1, 0)}
            )
            tweenOut:Play()
            tweenOut.Completed:Connect(function()
                notificationFrame.Visible = false
            end)
        end)
    end
end

-- Mostra indicador de dano
local function showDamageIndicator()
    damageIndicator.Transparency = 0.5
    local tween = TweenService:Create(
        damageIndicator,
        TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {Transparency = 1}
    )
    tween:Play()
end

-- Eventos de RemoteEvents
collectResourceEvent.OnClientEvent:Connect(function(value)
    showNotification("Recurso coletado! Valor: " .. value, Color3.new(0.2, 0.8, 0.2))
end)

playerDamagedEvent.OnClientEvent:Connect(function(damage, attackerName)
    showNotification("Você recebeu " .. damage .. " de dano de " .. attackerName, Color3.new(0.8, 0.2, 0.2))
    showDamageIndicator()
end)

depositResourceEvent.OnClientEvent:Connect(function(value, newBaseSize)
    showNotification("Recurso depositado! Valor: " .. value .. " | Tamanho da base: " .. newBaseSize, Color3.new(0.2, 0.2, 0.8))
end)

-- Conecta eventos do jogador
player.CharacterAdded:Connect(function(character)
    local humanoid = character:WaitForChild("Humanoid")
    
    -- Conecta mudanças de vida
    humanoid.HealthChanged:Connect(updateHealthBar)
    
    -- Conecta morte
    humanoid.Died:Connect(function()
        showNotification("Você morreu! Respawnando...", Color3.new(0.8, 0.2, 0.2), 5)
    end)
    
    -- Atualiza imediatamente
    updateHealthBar()
end)

-- Loop de atualização
RunService.Heartbeat:Connect(function()
    updateResourceStatus()
end)

-- Inicializa a UI
createGameplayUI()

-- Atualiza se já tem character
if player.Character then
    updateHealthBar()
end

print("GameplayUI inicializada com sucesso!")

return GameplayUI