-- CombatManager.lua
-- Script responsável por gerenciar o combate PvP no servidor

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

-- Aguarda dependências
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireWeaponEvent = remoteEventsFolder:WaitForChild("FireWeapon")
local playerDamagedEvent = remoteEventsFolder:WaitForChild("PlayerDamaged")

-- Carrega o BaseController
local BaseController = require(script.Parent.BaseController)

local CombatManager = {}

-- Configurações de combate
local DAMAGE_AMOUNT = 25
local WEAPON_RANGE = 500
local RESPAWN_TIME_SOLO = 10
local RESPAWN_TIME_TEAM = 15

-- C<PERSON> de bases dos jogadores
local playerBases = {}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    if playerBases[player] then
        return playerBases[player]
    end
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                playerBases[player] = base
                return base
            end
        end
    end
    
    return nil
end

-- Função para verificar se dois jogadores são da mesma equipe
local function areTeammates(player1, player2)
    if player1 == player2 then return true end
    
    local base1 = getPlayerBase(player1)
    local base2 = getPlayerBase(player2)
    
    return base1 and base2 and base1 == base2
end

-- Função para reduzir o tamanho da base
local function reduceBaseSize(base, amount)
    if not base then return end
    
    local baseSizeValue = base:FindFirstChild("BaseSize")
    if baseSizeValue then
        baseSizeValue.Value = math.max(0, baseSizeValue.Value - amount)
        print("Base " .. base.Name .. " teve seu tamanho reduzido para " .. baseSizeValue.Value)
        
        -- Atualiza visualmente o tamanho da base usando o BaseController
        BaseController.UpdateBaseSize(base)
    end
end

-- Função para aplicar dano a um jogador
local function damagePlayer(victim, attacker, damage)
    if not victim.Character or not victim.Character:FindFirstChild("Humanoid") then
        return false
    end
    
    local humanoid = victim.Character.Humanoid
    
    -- Verifica se são da mesma equipe
    if areTeammates(victim, attacker) then
        return false -- Não pode atacar companheiros de equipe
    end
    
    -- Aplica dano
    humanoid.Health = humanoid.Health - damage
    
    -- Notifica o cliente sobre o dano
    playerDamagedEvent:FireClient(victim, damage, attacker.Name)
    
    print(attacker.Name .. " causou " .. damage .. " de dano a " .. victim.Name)
    
    return true
end

-- Função para lidar com a morte de um jogador
local function handlePlayerDeath(player)
    print(player.Name .. " morreu!")
    
    -- Encontra a base do jogador
    local base = getPlayerBase(player)
    if base then
        -- Reduz o tamanho da base
        reduceBaseSize(base, 10)
    end
    
    -- Verifica se estava carregando recurso
    if player.Character and player.Character:FindFirstChild("CarregandoRecurso") then
        local carrying = player.Character.CarregandoRecurso.Value
        if carrying then
            print(player.Name .. " perdeu o recurso que estava carregando!")
            -- O recurso é perdido (já será removido quando o character for destruído)
        end
    end
    
    -- Determina tempo de respawn
    local respawnTime = RESPAWN_TIME_SOLO
    if base then
        local partner = base:FindFirstChild("Partner")
        local owner = base:FindFirstChild("Owner")
        
        -- Se tem parceiro, tempo de respawn é maior
        if (owner and owner.Value and owner.Value ~= player) or 
           (partner and partner.Value and partner.Value ~= player) then
            respawnTime = RESPAWN_TIME_TEAM
        end
    end
    
    -- Agenda o respawn
    spawn(function()
        wait(respawnTime)
        if player.Parent then -- Verifica se o jogador ainda está no jogo
            player:LoadCharacter()
            print(player.Name .. " respawnou após " .. respawnTime .. " segundos")
        end
    end)
end

-- Manipula disparos de arma
fireWeaponEvent.OnServerEvent:Connect(function(player, targetPosition, hitPart)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local startPosition = player.Character.HumanoidRootPart.Position
    local distance = (targetPosition - startPosition).Magnitude
    
    -- Verifica alcance
    if distance > WEAPON_RANGE then
        return
    end
    
    -- Verifica se atingiu um jogador
    if hitPart and hitPart.Parent:FindFirstChild("Humanoid") then
        local victim = Players:GetPlayerFromCharacter(hitPart.Parent)
        if victim and victim ~= player then
            damagePlayer(victim, player, DAMAGE_AMOUNT)
        end
    end
end)

-- Conecta ao evento de morte dos jogadores
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function(character)
        local humanoid = character:WaitForChild("Humanoid")
        
        humanoid.Died:Connect(function()
            handlePlayerDeath(player)
        end)
    end)
end)

-- Limpa cache quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playerBases[player] = nil
end)

print("CombatManager inicializado com sucesso!")

return CombatManager