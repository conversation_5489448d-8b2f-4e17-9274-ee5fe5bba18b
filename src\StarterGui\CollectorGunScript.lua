-- CollectorGunScript.lua
-- LocalScript para controlar a CollectorGun

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Aguarda os RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollectingEvent = remoteEventsFolder:WaitForChild("StartCollecting")
local stopCollectingEvent = remoteEventsFolder:WaitForChild("StopCollecting")
local startBaseAttackEvent = remoteEventsFolder:WaitForChild("StartBaseAttack")
local stopBaseAttackEvent = remoteEventsFolder:WaitForChild("StopBaseAttack")

-- Variáveis de controle
local equipped = false
local collecting = false
local attackingBase = false
local currentTool = nil
local currentTarget = nil
local mouseDownConnection = nil
local mouseUpConnection = nil

-- Função para criar beam visual
local function createBeam(startPos, endPos, isAttack)
    if not currentTool or not currentTool.Handle then return end
    
    -- Remove beam anterior
    local oldBeam = currentTool.Handle:FindFirstChild("CollectionBeam")
    if oldBeam then oldBeam:Destroy() end
    
    local attachment0 = Instance.new("Attachment")
    attachment0.Position = Vector3.new(0, 0, -1)
    attachment0.Parent = currentTool.Handle
    
    local attachment1 = Instance.new("Attachment")
    attachment1.WorldPosition = endPos
    attachment1.Parent = workspace.Terrain
    
    local beam = Instance.new("Beam")
    beam.Name = "CollectionBeam"
    beam.Attachment0 = attachment0
    beam.Attachment1 = attachment1
    beam.Width0 = 0.5
    beam.Width1 = 0.5
    beam.FaceCamera = true
    beam.Parent = currentTool.Handle
    
    if isAttack then
        beam.Color = ColorSequence.new(Color3.new(1, 0, 0))
    else
        beam.Color = ColorSequence.new(Color3.new(0, 0.8, 1))
    end
    
    -- Remove attachments após um tempo
    game:GetService("Debris"):AddItem(attachment1, 0.1)
    game:GetService("Debris"):AddItem(beam, 0.1)
end

-- Função para encontrar alvo sob o cursor
local function findTargetUnderCursor()
    local mouse = player:GetMouse()
    local target = mouse.Target
    
    if not target then return nil, nil end
    
    -- Verifica se é um recurso
    if target:FindFirstChild("ResourceType") then
        return target, "resource"
    end
    
    -- Verifica se é uma base (Barrier ou BasePlatform)
    if target.Name == "Barrier" or target.Name == "BasePlatform" then
        local baseModel = target.Parent
        if baseModel and baseModel.Name:match("Base_") then
            return target, "base"
        end
    end
    
    return nil, nil
end

-- Inicia ação
local function startAction()
    if collecting or attackingBase then return end
    
    local target, targetType = findTargetUnderCursor()
    if not target then return end
    
    currentTarget = target
    
    if targetType == "resource" then
        collecting = true
        startCollectingEvent:FireServer(target)
        
        if currentTool and currentTool.Parent and currentTool.Parent:FindFirstChild("Head") then
            createBeam(currentTool.Parent.Head.Position, target.Position, false)
        end
        
        local beamConnection
        beamConnection = RunService.Heartbeat:Connect(function()
            if not collecting or not currentTarget or not currentTarget.Parent then
                beamConnection:Disconnect()
                return
            end
            
            if currentTool and currentTool.Parent and currentTool.Parent:FindFirstChild("Head") then
                createBeam(currentTool.Parent.Head.Position, currentTarget.Position, false)
            end
        end)
        
    elseif targetType == "base" then
        attackingBase = true
        startBaseAttackEvent:FireServer(target)
        
        if currentTool and currentTool.Parent and currentTool.Parent:FindFirstChild("Head") then
            createBeam(currentTool.Parent.Head.Position, target.Position, true)
        end
        
        local beamConnection
        beamConnection = RunService.Heartbeat:Connect(function()
            if not attackingBase or not currentTarget or not currentTarget.Parent then
                beamConnection:Disconnect()
                return
            end
            
            if currentTool and currentTool.Parent and currentTool.Parent:FindFirstChild("Head") then
                createBeam(currentTool.Parent.Head.Position, currentTarget.Position, true)
            end
        end)
    end
    
    local sound = currentTool.Handle:FindFirstChild("CollectSound")
    if sound then
        sound:Play()
    end
end

-- Para ação
local function stopAction()
    if collecting then
        collecting = false
        if currentTarget then
            stopCollectingEvent:FireServer(currentTarget)
        end
    end
    
    if attackingBase then
        attackingBase = false
        stopBaseAttackEvent:FireServer()
    end
    
    currentTarget = nil
    
    -- Remove beam
    if currentTool and currentTool.Handle then
        local beam = currentTool.Handle:FindFirstChild("CollectionBeam")
        if beam then beam:Destroy() end
    end
end

-- Conecta eventos quando a ferramenta é equipada
local function onToolEquipped(tool)
    if tool.Name ~= "CollectorGun" then return end
    
    equipped = true
    currentTool = tool
    
    local mouse = player:GetMouse()
    
    mouseDownConnection = mouse.Button1Down:Connect(function()
        if equipped then
            startAction()
        end
    end)
    
    mouseUpConnection = mouse.Button1Up:Connect(function()
        if equipped then
            stopAction()
        end
    end)
    
    tool.Unequipped:Connect(function()
        equipped = false
        collecting = false
        attackingBase = false
        currentTool = nil
        
        if mouseDownConnection then
            mouseDownConnection:Disconnect()
            mouseDownConnection = nil
        end
        
        if mouseUpConnection then
            mouseUpConnection:Disconnect()
            mouseUpConnection = nil
        end
        
        stopAction()
    end)
end

-- Conecta eventos para ferramentas já existentes e futuras
local function connectToTools()
    -- Para ferramentas já no StarterPack
    local starterPack = game:GetService("StarterPack")
    for _, tool in ipairs(starterPack:GetChildren()) do
        if tool:IsA("Tool") and tool.Name == "CollectorGun" then
            tool.Equipped:Connect(function()
                onToolEquipped(tool)
            end)
        end
    end
    
    -- Para ferramentas adicionadas ao StarterPack
    starterPack.ChildAdded:Connect(function(child)
        if child:IsA("Tool") and child.Name == "CollectorGun" then
            child.Equipped:Connect(function()
                onToolEquipped(child)
            end)
        end
    end)
end

-- Conecta para o jogador atual
player.CharacterAdded:Connect(function(character)
    wait(1) -- Aguarda o character carregar
    
    -- Conecta para ferramentas no backpack
    local backpack = player:WaitForChild("Backpack")
    for _, tool in ipairs(backpack:GetChildren()) do
        if tool:IsA("Tool") and tool.Name == "CollectorGun" then
            tool.Equipped:Connect(function()
                onToolEquipped(tool)
            end)
        end
    end
    
    backpack.ChildAdded:Connect(function(child)
        if child:IsA("Tool") and child.Name == "CollectorGun" then
            child.Equipped:Connect(function()
                onToolEquipped(child)
            end)
        end
    end)
end)

-- Conecta para ferramentas já existentes
connectToTools()

print("CollectorGunScript inicializado!")
