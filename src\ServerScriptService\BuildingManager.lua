-- BuildingManager.lua
-- Script responsável por gerenciar o sistema de construção de defesas

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local requestBuildEvent = remoteEventsFolder:WaitForChild("RequestBuild")
local buildResponseEvent = remoteEventsFolder:WaitForChild("BuildResponse")

-- Carrega o BaseController
local BaseController = require(script.Parent.BaseController)

local BuildingManager = {}

-- Configurações de construção
local BUILDING_TYPES = {
    {
        id = "stone_wall",
        name = "Muro de Pedra",
        cost = 25,
        size = Vector3.new(8, 6, 2),
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Rock,
        health = 100,
        description = "Muro defensivo básico"
    },
    {
        id = "watch_tower",
        name = "Torre de Vigia",
        cost = 50,
        size = Vector3.new(4, 12, 4),
        color = BrickColor.new("Brown"),
        material = Enum.Material.Wood,
        health = 150,
        description = "Torre alta para observação"
    },
    {
        id = "barrier_generator",
        name = "Gerador de Barreira",
        cost = 75,
        size = Vector3.new(3, 3, 3),
        color = BrickColor.new("Cyan"),
        material = Enum.Material.Neon,
        health = 80,
        description = "Fortalece a barreira da base"
    },
    {
        id = "resource_storage",
        name = "Depósito de Recursos",
        cost = 40,
        size = Vector3.new(6, 4, 6),
        color = BrickColor.new("Bright yellow"),
        material = Enum.Material.Metal,
        health = 120,
        description = "Aumenta capacidade de materiais"
    }
}

-- Armazena construções ativas
local activeBuildings = {} -- [base] = {building1, building2, ...}

-- Cache de bases dos jogadores
local playerBases = {}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    if playerBases[player] then
        return playerBases[player]
    end
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                playerBases[player] = base
                return base
            end
        end
    end
    
    return nil
end

-- Função para obter tipo de construção por ID
local function getBuildingType(buildingId)
    for _, buildingType in ipairs(BUILDING_TYPES) do
        if buildingType.id == buildingId then
            return buildingType
        end
    end
    return nil
end

-- Função para verificar se uma posição é válida para construção
local function isValidBuildPosition(base, position, buildingSize)
    if not base then return false end
    
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return false end
    
    -- Verifica se está dentro da barreira
    local barrierRadius = barrier.Size.X / 2
    local barrierCenter = barrier.Position
    local distance = (position - barrierCenter).Magnitude
    
    if distance + (buildingSize.X / 2) > barrierRadius then
        return false -- Muito perto da borda da barreira
    end
    
    -- Verifica se não está muito perto da torre central
    local coreTower = base:FindFirstChild("CoreTower")
    if coreTower then
        local towerDistance = (position - coreTower.Position).Magnitude
        if towerDistance < 8 then
            return false -- Muito perto da torre
        end
    end
    
    -- Verifica colisão com outras construções
    local buildings = activeBuildings[base] or {}
    for _, building in ipairs(buildings) do
        if building and building.Parent then
            local buildingDistance = (position - building.Position).Magnitude
            local minDistance = (buildingSize.X + building.Size.X) / 2 + 2 -- 2 studs de margem
            if buildingDistance < minDistance then
                return false -- Muito perto de outra construção
            end
        end
    end
    
    return true
end

-- Função para criar uma construção
local function createBuilding(base, buildingType, position)
    local building = Instance.new("Part")
    building.Name = buildingType.name
    building.Size = buildingType.size
    building.Position = position
    building.BrickColor = buildingType.color
    building.Material = buildingType.material
    building.Anchored = true
    building.Parent = workspace
    
    -- Adiciona valores da construção
    local buildingId = Instance.new("StringValue")
    buildingId.Name = "BuildingId"
    buildingId.Value = buildingType.id
    buildingId.Parent = building
    
    local buildingHealth = Instance.new("NumberValue")
    buildingHealth.Name = "BuildingHealth"
    buildingHealth.Value = buildingType.health
    buildingHealth.Parent = building
    
    local maxHealth = Instance.new("NumberValue")
    maxHealth.Name = "MaxHealth"
    maxHealth.Value = buildingType.health
    maxHealth.Parent = building
    
    local ownerBase = Instance.new("ObjectValue")
    ownerBase.Name = "OwnerBase"
    ownerBase.Value = base
    ownerBase.Parent = building
    
    -- Adiciona à lista de construções da base
    if not activeBuildings[base] then
        activeBuildings[base] = {}
    end
    table.insert(activeBuildings[base], building)
    
    -- Efeito visual de construção
    local attachment = Instance.new("Attachment")
    attachment.Parent = building
    
    local particles = Instance.new("ParticleEmitter")
    particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
    particles.Color = ColorSequence.new(Color3.new(1, 1, 0))
    particles.Size = NumberSequence.new(1)
    particles.Lifetime = NumberRange.new(1, 2)
    particles.Rate = 50
    particles.SpreadAngle = Vector2.new(45, 45)
    particles.Speed = NumberRange.new(3, 6)
    particles.Parent = attachment
    
    -- Para as partículas após um tempo
    spawn(function()
        wait(3)
        particles.Enabled = false
        wait(2)
        attachment:Destroy()
    end)
    
    print("Construção " .. buildingType.name .. " criada na base " .. base.Name)
    return building
end

-- Função para destruir construções que estão fora da barreira
function BuildingManager.checkBuildingsInBarrier(base)
    local buildings = activeBuildings[base]
    if not buildings then return end
    
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return end
    
    local barrierRadius = barrier.Size.X / 2
    local barrierCenter = barrier.Position
    
    for i = #buildings, 1, -1 do
        local building = buildings[i]
        if building and building.Parent then
            local distance = (building.Position - barrierCenter).Magnitude
            local buildingRadius = math.max(building.Size.X, building.Size.Z) / 2
            
            -- Se a construção está fora da barreira, destrua-a
            if distance + buildingRadius > barrierRadius then
                print("Construção " .. building.Name .. " destruída por encolhimento da base!")
                
                -- Efeito de destruição
                local attachment = Instance.new("Attachment")
                attachment.Parent = building
                
                local explosion = Instance.new("ParticleEmitter")
                explosion.Texture = "rbxasset://textures/particles/fire_main.dds"
                explosion.Color = ColorSequence.new(Color3.new(1, 0.5, 0))
                explosion.Size = NumberSequence.new(2)
                explosion.Lifetime = NumberRange.new(0.5, 1.5)
                explosion.Rate = 100
                explosion.SpreadAngle = Vector2.new(180, 180)
                explosion.Speed = NumberRange.new(5, 15)
                explosion.Parent = attachment
                
                spawn(function()
                    wait(1)
                    explosion.Enabled = false
                    wait(2)
                    if building and building.Parent then
                        building:Destroy()
                    end
                end)
                
                table.remove(buildings, i)
            end
        else
            table.remove(buildings, i)
        end
    end
end

-- Manipula solicitações de construção
requestBuildEvent.OnServerEvent:Connect(function(player, buildingId, position)
    local base = getPlayerBase(player)
    if not base then
        buildResponseEvent:FireClient(player, "error", "Você não possui uma base!")
        return
    end
    
    local buildingType = getBuildingType(buildingId)
    if not buildingType then
        buildResponseEvent:FireClient(player, "error", "Tipo de construção inválido!")
        return
    end
    
    -- Verifica materiais suficientes
    local buildingMaterials = base:FindFirstChild("BuildingMaterials")
    if not buildingMaterials or buildingMaterials.Value < buildingType.cost then
        buildResponseEvent:FireClient(player, "error", "Materiais insuficientes! Necessário: " .. buildingType.cost)
        return
    end
    
    -- Verifica posição válida
    if not isValidBuildPosition(base, position, buildingType.size) then
        buildResponseEvent:FireClient(player, "error", "Posição inválida para construção!")
        return
    end
    
    -- Deduz o custo
    buildingMaterials.Value = buildingMaterials.Value - buildingType.cost
    
    -- Cria a construção
    local building = createBuilding(base, buildingType, position)
    
    -- Notifica sucesso
    buildResponseEvent:FireClient(player, "success", buildingType.name .. " construído com sucesso!")
    
    print(player.Name .. " construiu " .. buildingType.name .. " por " .. buildingType.cost .. " materiais")
end)

-- Função para obter tipos de construção (para a UI)
function BuildingManager.getBuildingTypes()
    return BUILDING_TYPES
end

-- Função para obter construções de uma base
function BuildingManager.getBaseBuildings(base)
    return activeBuildings[base] or {}
end

-- Limpa cache quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playerBases[player] = nil
end)

-- Limpa construções quando base é destruída
local originalDestroyBase = BaseController.DestroyBase
BaseController.DestroyBase = function(baseModel)
    -- Destroi todas as construções da base
    local buildings = activeBuildings[baseModel]
    if buildings then
        for _, building in ipairs(buildings) do
            if building and building.Parent then
                building:Destroy()
            end
        end
        activeBuildings[baseModel] = nil
    end
    
    -- Chama a função original
    originalDestroyBase(baseModel)
end

-- Conecta verificação de construções ao BaseController
local originalUpdateBaseSize = BaseController.UpdateBaseSize
BaseController.UpdateBaseSize = function(baseModel)
    local result = originalUpdateBaseSize(baseModel)
    
    -- Verifica construções após atualizar o tamanho
    BuildingManager.checkBuildingsInBarrier(baseModel)
    
    return result
end

print("BuildingManager inicializado com sucesso!")

return BuildingManager