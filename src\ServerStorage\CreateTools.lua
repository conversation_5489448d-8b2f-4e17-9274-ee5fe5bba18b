-- CreateTools.lua
-- <PERSON><PERSON><PERSON> para criar as ferramentas CombatGun e CollectorGun diretamente no StarterPack

local ServerStorage = game:GetService("ServerStorage")
local StarterPack = game:GetService("StarterPack")

-- Força a criação/verificação do StarterPack
require(ServerStorage.ForceCreateStarterPack)

local function createCombatGun()
    -- Remove ferramenta existente se houver
    local existingTool = StarterPack:FindFirstChild("CombatGun")
    if existingTool then
        existingTool:Destroy()
    end
    
    -- Cria a ferramenta
    local tool = Instance.new("Tool")
    tool.Name = "CombatGun"
    tool.RequiresHandle = true
    tool.Parent = StarterPack
    
    -- Cria o handle (cabo da arma)
    local handle = Instance.new("Part")
    handle.Name = "Handle"
    handle.Size = Vector3.new(0.5, 1, 3)
    handle.BrickColor = BrickColor.new("Really black")
    handle.Material = Enum.Material.Metal
    handle.Shape = Enum.PartType.Block
    handle.Parent = tool
    
    -- Adiciona detalhes visuais
    local barrel = Instance.new("Part")
    barrel.Name = "Barrel"
    barrel.Size = Vector3.new(0.3, 0.3, 2)
    barrel.BrickColor = BrickColor.new("Dark stone grey")
    barrel.Material = Enum.Material.Metal
    barrel.Shape = Enum.PartType.Cylinder
    barrel.CanCollide = false
    barrel.Parent = tool
    
    -- Weld para conectar o cano ao cabo
    local weld = Instance.new("WeldConstraint")
    weld.Part0 = handle
    weld.Part1 = barrel
    weld.Parent = handle
    
    -- Posiciona o cano
    barrel.CFrame = handle.CFrame * CFrame.new(0, 0, -1.5)
    
    -- Adiciona som de disparo
    local fireSound = Instance.new("Sound")
    fireSound.Name = "FireSound"
    fireSound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    fireSound.Volume = 0.5
    fireSound.Parent = handle
    
    -- Cria o script local
    local script = Instance.new("LocalScript")
    script.Name = "CombatGunScript"
    script.Source = [[
-- CombatGun LocalScript
local tool = script.Parent
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda os RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local fireWeaponEvent = remoteEventsFolder:WaitForChild("FireWeapon")

-- Configurações da arma
local FIRE_RATE = 0.3
local PROJECTILE_SPEED = 200
local RANGE = 500

local lastFireTime = 0
local equipped = false

-- Cria o projétil visual
local function createProjectile(startPos, direction)
    local projectile = Instance.new("Part")
    projectile.Name = "Projectile"
    projectile.Size = Vector3.new(0.5, 0.5, 2)
    projectile.Shape = Enum.PartType.Cylinder
    projectile.BrickColor = BrickColor.new("Bright yellow")
    projectile.Material = Enum.Material.Neon
    projectile.CanCollide = false
    projectile.Anchored = true
    projectile.Parent = workspace
    
    projectile.CFrame = CFrame.lookAt(startPos, startPos + direction)
    
    local startTime = tick()
    local connection
    connection = RunService.Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local distance = PROJECTILE_SPEED * elapsed
        
        if distance >= RANGE then
            projectile:Destroy()
            connection:Disconnect()
            return
        end
        
        local newPos = startPos + direction * distance
        projectile.Position = newPos
        
        local raycast = workspace:Raycast(projectile.Position, direction * 5)
        if raycast and raycast.Instance.Parent:FindFirstChild("Humanoid") then
            projectile:Destroy()
            connection:Disconnect()
        end
    end)
    
    Debris:AddItem(projectile, 3)
end

-- Função de disparo
local function fire()
    local currentTime = tick()
    if currentTime - lastFireTime < FIRE_RATE then
        return
    end
    
    lastFireTime = currentTime
    
    if not tool.Parent or not tool.Parent:FindFirstChild("Head") then
        return
    end
    
    local character = tool.Parent
    local head = character:FindFirstChild("Head")
    
    local startPos = head.Position
    local direction = (mouse.Hit.Position - startPos).Unit
    
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
    
    createProjectile(startPos, direction)
    
    if raycastResult then
        fireWeaponEvent:FireServer(raycastResult.Position, raycastResult.Instance)
    else
        fireWeaponEvent:FireServer(startPos + direction * RANGE, nil)
    end
    
    local sound = tool.Handle:FindFirstChild("FireSound")
    if sound then
        sound:Play()
    end
end

-- Eventos da ferramenta
tool.Equipped:Connect(function()
    equipped = true
end)

tool.Unequipped:Connect(function()
    equipped = false
end)

tool.Activated:Connect(function()
    if equipped then
        fire()
    end
end)
    ]]
    script.Parent = tool
    
    print("CombatGun criada com sucesso!")
    return tool
end

local function createCollectorGun()
    -- Remove ferramenta existente se houver
    local existingTool = StarterPack:FindFirstChild("CollectorGun")
    if existingTool then
        existingTool:Destroy()
    end
    
    -- Cria a ferramenta
    local tool = Instance.new("Tool")
    tool.Name = "CollectorGun"
    tool.RequiresHandle = true
    tool.Parent = StarterPack
    
    -- Cria o handle
    local handle = Instance.new("Part")
    handle.Name = "Handle"
    handle.Size = Vector3.new(0.5, 1, 2.5)
    handle.BrickColor = BrickColor.new("Bright blue")
    handle.Material = Enum.Material.Plastic
    handle.Shape = Enum.PartType.Block
    handle.Parent = tool
    
    -- Adiciona detalhes visuais
    local collector = Instance.new("Part")
    collector.Name = "Collector"
    collector.Size = Vector3.new(0.8, 0.8, 1)
    collector.BrickColor = BrickColor.new("Cyan")
    collector.Material = Enum.Material.Neon
    collector.Shape = Enum.PartType.Block
    collector.CanCollide = false
    collector.Parent = tool
    
    -- Weld para conectar
    local weld = Instance.new("WeldConstraint")
    weld.Part0 = handle
    weld.Part1 = collector
    weld.Parent = handle
    
    -- Posiciona o coletor
    collector.CFrame = handle.CFrame * CFrame.new(0, 0, -1.2)
    
    -- Adiciona som de coleta
    local collectSound = Instance.new("Sound")
    collectSound.Name = "CollectSound"
    collectSound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    collectSound.Volume = 0.3
    collectSound.Pitch = 1.5
    collectSound.Parent = handle
    
    -- Cria o script local
    local script = Instance.new("LocalScript")
    script.Name = "CollectorGunScript"
    script.Source = [[
-- CollectorGun LocalScript
local tool = script.Parent
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Aguarda os RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollectingEvent = remoteEventsFolder:WaitForChild("StartCollecting")
local stopCollectingEvent = remoteEventsFolder:WaitForChild("StopCollecting")
local startBaseAttackEvent = remoteEventsFolder:WaitForChild("StartBaseAttack")
local stopBaseAttackEvent = remoteEventsFolder:WaitForChild("StopBaseAttack")

-- Configurações
local COLLECTION_RANGE = 100
local BASE_ATTACK_RANGE = 150
local BEAM_WIDTH = 2

-- Variáveis de estado
local equipped = false
local collecting = false
local attackingBase = false
local currentTarget = nil
local collectionBeam = nil
local mouseDownConnection = nil
local mouseUpConnection = nil

-- Cria o feixe visual
local function createBeam(startPos, endPos, isAttack)
    if collectionBeam then
        collectionBeam:Destroy()
    end
    
    local startAttachment = Instance.new("Attachment")
    local endAttachment = Instance.new("Attachment")
    
    local startPart = Instance.new("Part")
    startPart.Size = Vector3.new(0.1, 0.1, 0.1)
    startPart.Transparency = 1
    startPart.CanCollide = false
    startPart.Anchored = true
    startPart.Position = startPos
    startPart.Parent = workspace
    startAttachment.Parent = startPart
    
    endAttachment.Parent = currentTarget
    
    collectionBeam = Instance.new("Beam")
    collectionBeam.Attachment0 = startAttachment
    collectionBeam.Attachment1 = endAttachment
    collectionBeam.Width0 = BEAM_WIDTH
    collectionBeam.Width1 = BEAM_WIDTH
    
    if isAttack then
        collectionBeam.Color = ColorSequence.new(Color3.new(1, 0, 0))
        collectionBeam.Transparency = NumberSequence.new(0.2)
    else
        collectionBeam.Color = ColorSequence.new(Color3.new(0, 1, 0))
        collectionBeam.Transparency = NumberSequence.new(0.3)
    end
    
    collectionBeam.FaceCamera = true
    collectionBeam.Parent = startPart
    
    game:GetService("Debris"):AddItem(startPart, 0.1)
end

-- Encontra alvo sob o cursor
local function findTargetUnderCursor()
    if not tool.Parent or not tool.Parent:FindFirstChild("Head") then
        return nil, nil
    end
    
    local character = tool.Parent
    local head = character:FindFirstChild("Head")
    local startPos = head.Position
    local direction = (mouse.Hit.Position - startPos).Unit
    
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * COLLECTION_RANGE, raycastParams)
    
    if raycastResult then
        local hitPart = raycastResult.Instance
        
        if hitPart:FindFirstChild("OriginalSize") and hitPart:FindFirstChild("ResourceType") then
            return hitPart, "resource"
        end
        
        if hitPart.Name == "Barrier" or hitPart.Name == "BasePlatform" then
            local distance = (startPos - hitPart.Position).Magnitude
            if distance <= BASE_ATTACK_RANGE then
                return hitPart, "base"
            end
        end
    end
    
    return nil, nil
end

-- Inicia ação
local function startAction()
    if collecting or attackingBase then return end
    
    local target, targetType = findTargetUnderCursor()
    if not target then return end
    
    currentTarget = target
    
    if targetType == "resource" then
        collecting = true
        startCollectingEvent:FireServer(target)
        
        if tool.Parent and tool.Parent:FindFirstChild("Head") then
            createBeam(tool.Parent.Head.Position, target.Position, false)
        end
        
        local beamConnection
        beamConnection = RunService.Heartbeat:Connect(function()
            if not collecting or not currentTarget or not currentTarget.Parent then
                beamConnection:Disconnect()
                return
            end
            
            if tool.Parent and tool.Parent:FindFirstChild("Head") then
                createBeam(tool.Parent.Head.Position, currentTarget.Position, false)
            end
        end)
        
    elseif targetType == "base" then
        attackingBase = true
        startBaseAttackEvent:FireServer(target)
        
        if tool.Parent and tool.Parent:FindFirstChild("Head") then
            createBeam(tool.Parent.Head.Position, target.Position, true)
        end
        
        local beamConnection
        beamConnection = RunService.Heartbeat:Connect(function()
            if not attackingBase or not currentTarget or not currentTarget.Parent then
                beamConnection:Disconnect()
                return
            end
            
            if tool.Parent and tool.Parent:FindFirstChild("Head") then
                createBeam(tool.Parent.Head.Position, currentTarget.Position, true)
            end
        end)
    end
end

-- Para ação
local function stopAction()
    if collecting then
        collecting = false
        if currentTarget then
            stopCollectingEvent:FireServer(currentTarget)
        end
    elseif attackingBase then
        attackingBase = false
        stopBaseAttackEvent:FireServer()
    end
    
    currentTarget = nil
    
    if collectionBeam then
        collectionBeam:Destroy()
        collectionBeam = nil
    end
end

-- Eventos da ferramenta
tool.Equipped:Connect(function()
    equipped = true
    
    mouseDownConnection = mouse.Button1Down:Connect(function()
        if equipped then
            startAction()
        end
    end)
    
    mouseUpConnection = mouse.Button1Up:Connect(function()
        if equipped then
            stopAction()
        end
    end)
end)

tool.Unequipped:Connect(function()
    equipped = false
    collecting = false
    attackingBase = false
    
    if mouseDownConnection then
        mouseDownConnection:Disconnect()
        mouseDownConnection = nil
    end
    
    if mouseUpConnection then
        mouseUpConnection:Disconnect()
        mouseUpConnection = nil
    end
    
    stopAction()
end)
    ]]
    script.Parent = tool
    
    print("CollectorGun criada com sucesso!")
    return tool
end

-- Cria as ferramentas
createCombatGun()
createCollectorGun()

print("Todas as ferramentas foram criadas com sucesso no StarterPack!")