-- CreateBasicMap.lua
-- Script para criar um mapa básico para o jogo

local Lighting = game:GetService("Lighting")
local Workspace = game:GetService("Workspace")

local function createBasicMap()
    -- Remove objetos padrão desnecessários
    for _, obj in ipairs(Workspace:GetChildren()) do
        if obj.Name == "Baseplate" or obj.Name == "SpawnLocation" then
            obj:<PERSON><PERSON><PERSON>()
        end
    end
    
    -- C<PERSON> o chão principal
    local baseplate = Instance.new("Part")
    baseplate.Name = "Baseplate"
    baseplate.Size = Vector3.new(1000, 4, 1000)
    baseplate.Position = Vector3.new(0, -2, 0)
    baseplate.BrickColor = BrickColor.new("Bright green")
    baseplate.Material = Enum.Material.Grass
    baseplate.Anchored = true
    baseplate.Parent = Workspace
    
    -- NÃO adiciona textura para evitar mudanças visuais
    
    -- Cria spawn central temporário
    local centralSpawn = Instance.new("SpawnLocation")
    centralSpawn.Name = "CentralSpawn"
    centralSpawn.Size = Vector3.new(12, 1, 12)
    centralSpawn.Position = Vector3.new(0, 1, 0)
    centralSpawn.BrickColor = BrickColor.new("Bright blue")
    centralSpawn.Material = Enum.Material.Neon
    centralSpawn.Anchored = true
    centralSpawn.Parent = Workspace
    
    -- Adiciona algumas decorações
    local decorations = {
        {pos = Vector3.new(300, 10, 300), size = Vector3.new(20, 20, 20), color = "Brown", material = Enum.Material.Rock},
        {pos = Vector3.new(-300, 10, 300), size = Vector3.new(15, 30, 15), color = "Dark green", material = Enum.Material.Grass},
        {pos = Vector3.new(300, 10, -300), size = Vector3.new(25, 15, 25), color = "Dark stone grey", material = Enum.Material.Concrete},
        {pos = Vector3.new(-300, 10, -300), size = Vector3.new(18, 25, 18), color = "Reddish brown", material = Enum.Material.Wood}
    }
    
    for i, decoration in ipairs(decorations) do
        local part = Instance.new("Part")
        part.Name = "Decoration" .. i
        part.Size = decoration.size
        part.Position = decoration.pos
        part.BrickColor = BrickColor.new(decoration.color)
        part.Material = decoration.material
        part.Anchored = true
        part.Parent = Workspace
    end
    
    -- Configura iluminação básica
    Lighting.Brightness = 2
    Lighting.Ambient = Color3.new(0.2, 0.2, 0.2)
    Lighting.TimeOfDay = "14:00:00"
    
    -- Adiciona skybox se disponível
    local sky = Instance.new("Sky")
    sky.SkyboxBk = "rbxasset://textures/sky/sky512_bk.jpg"
    sky.SkyboxDn = "rbxasset://textures/sky/sky512_dn.jpg"
    sky.SkyboxFt = "rbxasset://textures/sky/sky512_ft.jpg"
    sky.SkyboxLf = "rbxasset://textures/sky/sky512_lf.jpg"
    sky.SkyboxRt = "rbxasset://textures/sky/sky512_rt.jpg"
    sky.SkyboxUp = "rbxasset://textures/sky/sky512_up.jpg"
    sky.Parent = Lighting
    
    print("Mapa básico criado com sucesso!")
    print("- Baseplate: 1000x1000 studs")
    print("- Spawn central temporário")
    print("- 4 decorações nos cantos")
    print("- Iluminação configurada")
end

-- Executa a criação do mapa
createBasicMap()

-- Retorna true para indicar sucesso
return true