-- CreateBaseTemplate.lua
-- Script para criar o modelo de base template
-- Execute este script uma vez no ServerStorage para criar o BaseTemplate

local ServerStorage = game:GetService("ServerStorage")

local function createBaseTemplate()
    -- Verifica se já existe
    if ServerStorage:FindFirstChild("BaseTemplate") then
        ServerStorage.BaseTemplate:Destroy()
    end
    
    -- Cria o modelo principal
    local baseTemplate = Instance.new("Model")
    baseTemplate.Name = "BaseTemplate"
    baseTemplate.Parent = ServerStorage
    
    -- 1. BasePlatform (chão de grama cilíndrico)
    local basePlatform = Instance.new("Part")
    basePlatform.Name = "BasePlatform"
    basePlatform.Shape = Enum.PartType.Cylinder
    basePlatform.Size = Vector3.new(2, 30, 30) -- <PERSON>ilind<PERSON> deitado
    basePlatform.Position = Vector3.new(0, 1, 0)
    basePlatform.BrickColor = BrickColor.new("Bright green")
    basePlatform.Material = Enum.Material.Grass
    basePlatform.Anchored = true
    basePlatform.Parent = baseTemplate
    
    -- 2. CoreTower (torre central alta e fina)
    local coreTower = Instance.new("Part")
    coreTower.Name = "CoreTower"
    coreTower.Size = Vector3.new(2, 20, 2)
    coreTower.Position = Vector3.new(0, 11, 0)
    coreTower.BrickColor = BrickColor.new("Dark stone grey")
    coreTower.Material = Enum.Material.Concrete
    coreTower.Anchored = true
    coreTower.Parent = baseTemplate
    
    -- Bandeira no topo da torre
    local flag = Instance.new("Part")
    flag.Name = "Flag"
    flag.Size = Vector3.new(0.2, 4, 6)
    flag.Position = Vector3.new(3, 19, 0)
    flag.BrickColor = BrickColor.new("Really red")
    flag.Material = Enum.Material.Fabric
    flag.Anchored = true
    flag.Parent = coreTower
    
    -- Haste da bandeira
    local flagPole = Instance.new("Part")
    flagPole.Name = "FlagPole"
    flagPole.Size = Vector3.new(0.5, 8, 0.5)
    flagPole.Position = Vector3.new(1.75, 19, 0)
    flagPole.BrickColor = BrickColor.new("Really black")
    flagPole.Material = Enum.Material.Metal
    flagPole.Anchored = true
    flagPole.Parent = coreTower
    
    -- 3. SpawnLocation
    local spawnLocation = Instance.new("SpawnLocation")
    spawnLocation.Name = "SpawnLocation"
    spawnLocation.Size = Vector3.new(6, 1, 6)
    spawnLocation.Position = Vector3.new(0, 2.5, 0)
    spawnLocation.BrickColor = BrickColor.new("Bright blue")
    spawnLocation.Material = Enum.Material.Neon
    spawnLocation.Anchored = true
    spawnLocation.Enabled = false -- Desabilitado por padrão
    spawnLocation.Parent = baseTemplate
    
    -- 4. Barrier (barreira esférica semi-transparente) - altura reduzida
    local barrier = Instance.new("Part")
    barrier.Name = "Barrier"
    barrier.Shape = Enum.PartType.Ball
    barrier.Size = Vector3.new(40, 8, 40) -- Altura reduzida de 40 para 8
    barrier.Position = Vector3.new(0, 4.5, 0) -- Posição ajustada para tocar o chão (altura/2 + 0.5)
    barrier.BrickColor = BrickColor.new("Cyan")
    barrier.Material = Enum.Material.ForceField
    barrier.Transparency = 0.7
    barrier.CanCollide = false
    barrier.Anchored = true
    barrier.Parent = baseTemplate
    
    -- 5. ClaimPad (pad de reivindicação no chão)
    local claimPad = Instance.new("Part")
    claimPad.Name = "ClaimPad"
    claimPad.Size = Vector3.new(8, 0.5, 8)
    claimPad.Position = Vector3.new(0, 0.25, 15)
    claimPad.BrickColor = BrickColor.new("Bright yellow")
    claimPad.Material = Enum.Material.Neon
    claimPad.Anchored = true
    claimPad.Parent = baseTemplate
    
    -- Texto no ClaimPad
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = claimPad
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "CLAIM BASE"
    textLabel.TextColor3 = Color3.new(0, 0, 0)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui
    
    -- 6. Valores de propriedade
    local owner = Instance.new("ObjectValue")
    owner.Name = "Owner"
    owner.Value = nil
    owner.Parent = baseTemplate
    
    local partner = Instance.new("ObjectValue")
    partner.Name = "Partner"
    partner.Value = nil
    partner.Parent = baseTemplate
    
    local baseSize = Instance.new("NumberValue")
    baseSize.Name = "BaseSize"
    baseSize.Value = 100
    baseSize.Parent = baseTemplate
    
    local buildingMaterials = Instance.new("IntValue")
    buildingMaterials.Name = "BuildingMaterials"
    buildingMaterials.Value = 0
    buildingMaterials.Parent = baseTemplate
    
    -- Define o PrimaryPart para facilitar o posicionamento
    baseTemplate.PrimaryPart = basePlatform
    
    print("BaseTemplate criado com sucesso em ServerStorage!")
    return baseTemplate
end

-- Executa a criação
createBaseTemplate()

-- Retorna true para indicar sucesso
return true