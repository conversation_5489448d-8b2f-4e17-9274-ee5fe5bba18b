-- CreateToolsFixed.lua
-- Script para criar as ferramentas sem usar Source (que precisa de permissões especiais)

local ServerStorage = game:GetService("ServerStorage")
local StarterPack = game:GetService("StarterPack")

-- Força a criação/verificação do StarterPack
require(ServerStorage.ForceCreateStarterPack)

local function createCombatGun()
    -- Remove ferramenta existente se houver
    local existingTool = StarterPack:FindFirstChild("CombatGun")
    if existingTool then
        existingTool:Destroy()
    end
    
    -- Cria a ferramenta
    local tool = Instance.new("Tool")
    tool.Name = "CombatGun"
    tool.RequiresHandle = true
    tool.Parent = StarterPack
    
    -- Cria o handle (cabo da arma)
    local handle = Instance.new("Part")
    handle.Name = "Handle"
    handle.Size = Vector3.new(0.5, 1, 3)
    handle.BrickColor = BrickColor.new("Really black")
    handle.Material = Enum.Material.Metal
    handle.Shape = Enum.PartType.Block
    handle.Parent = tool
    
    -- Adiciona detalhes visuais
    local barrel = Instance.new("Part")
    barrel.Name = "Barrel"
    barrel.Size = Vector3.new(0.3, 0.3, 2)
    barrel.BrickColor = BrickColor.new("Dark stone grey")
    barrel.Material = Enum.Material.Metal
    barrel.Shape = Enum.PartType.Cylinder
    barrel.CanCollide = false
    barrel.Parent = tool
    
    -- Weld para conectar o cano ao cabo
    local weld = Instance.new("WeldConstraint")
    weld.Part0 = handle
    weld.Part1 = barrel
    weld.Parent = handle
    
    -- Posiciona o cano
    barrel.CFrame = handle.CFrame * CFrame.new(0, 0, -1.5)
    
    -- Adiciona som de disparo
    local fireSound = Instance.new("Sound")
    fireSound.Name = "FireSound"
    fireSound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    fireSound.Volume = 0.5
    fireSound.Parent = handle
    
    print("CombatGun criada com sucesso!")
    return tool
end

local function createCollectorGun()
    -- Remove ferramenta existente se houver
    local existingTool = StarterPack:FindFirstChild("CollectorGun")
    if existingTool then
        existingTool:Destroy()
    end
    
    -- Cria a ferramenta
    local tool = Instance.new("Tool")
    tool.Name = "CollectorGun"
    tool.RequiresHandle = true
    tool.Parent = StarterPack
    
    -- Cria o handle
    local handle = Instance.new("Part")
    handle.Name = "Handle"
    handle.Size = Vector3.new(0.5, 1, 2.5)
    handle.BrickColor = BrickColor.new("Bright blue")
    handle.Material = Enum.Material.Plastic
    handle.Shape = Enum.PartType.Block
    handle.Parent = tool
    
    -- Adiciona detalhes visuais
    local collector = Instance.new("Part")
    collector.Name = "Collector"
    collector.Size = Vector3.new(0.8, 0.8, 1)
    collector.BrickColor = BrickColor.new("Cyan")
    collector.Material = Enum.Material.Neon
    collector.Shape = Enum.PartType.Block
    collector.CanCollide = false
    collector.Parent = tool
    
    -- Weld para conectar
    local weld = Instance.new("WeldConstraint")
    weld.Part0 = handle
    weld.Part1 = collector
    weld.Parent = handle
    
    -- Posiciona o coletor
    collector.CFrame = handle.CFrame * CFrame.new(0, 0, -1.2)
    
    -- Adiciona som de coleta
    local collectSound = Instance.new("Sound")
    collectSound.Name = "CollectSound"
    collectSound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    collectSound.Volume = 0.3
    collectSound.Pitch = 1.5
    collectSound.Parent = handle
    
    print("CollectorGun criada com sucesso!")
    return tool
end

-- Cria as ferramentas
createCombatGun()
createCollectorGun()

print("Todas as ferramentas foram criadas com sucesso no StarterPack!")
print("NOTA: Scripts das ferramentas devem ser adicionados via StarterGui ou outros métodos")

-- Retorna true para indicar sucesso
return true