-- GameInitializer.lua
-- Script principal que inicializa todos os sistemas do jogo na ordem correta

local ServerStorage = game:GetService("ServerStorage")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

print("=== INICIALIZANDO JOGO DE ARENA - FASE 4 ===")

-- 1. Configura o Workspace primeiro
print("1. Configurando Workspace...")
require(ServerStorage.ConfigureWorkspace)

-- 2. Cria o mapa básico
print("2. Criando mapa básico...")
require(ServerStorage.CreateBasicMap)

-- 3. Cria os RemoteEvents
print("3. Criando RemoteEvents...")
require(ReplicatedStorage.RemoteEvents)

-- 4. Cria o BaseTemplate
print("4. Criando BaseTemplate...")
require(ServerStorage.CreateBaseTemplate)

-- 5. <PERSON><PERSON> as ferramentas (CombatGun e CollectorGun)
print("5. <PERSON><PERSON><PERSON> ferramentas...")
require(ServerStorage.CreateTools)

-- 6. <PERSON>figura o StarterPlayer
print("6. Configurando StarterPlayer...")
require(ServerStorage.ConfigureStarterPlayer)

-- 7. Aguarda um pouco para garantir que tudo foi criado
wait(1)

-- 8. Inicializa o sistema de bases
print("8. Inicializando BaseManager...")
require(script.Parent.BaseManager)

-- 9. Inicializa o sistema de convites
print("9. Inicializando InviteManager...")
require(script.Parent.InviteManager)

-- 10. Inicializa o sistema de recursos
print("10. Inicializando ResourceManager...")
require(script.Parent.ResourceManager)

-- 11. Inicializa o sistema de coleta
print("11. Inicializando CollectionManager...")
require(script.Parent.CollectionManager)

-- 12. Inicializa o sistema de combate
print("12. Inicializando CombatManager...")
require(script.Parent.CombatManager)

-- 13. Inicializa o sistema de depósito
print("13. Inicializando DepositManager...")
require(script.Parent.DepositManager)

-- 14. Inicializa o sistema de ataque à base
print("14. Inicializando BaseAttackManager...")
require(script.Parent.BaseAttackManager)

-- 15. Inicializa o sistema de construção
print("15. Inicializando BuildingManager...")
require(script.Parent.BuildingManager)

print("=== JOGO INICIALIZADO COM SUCESSO - FASE 4 ===")
print("Instruções:")
print("BASES:")
print("- Toque em um ClaimPad amarelo para reivindicar uma base")
print("- Use o botão CONVIDAR para formar duplas")
print("- Cada base pode ter no máximo 2 jogadores (dono + parceiro)")
print("")
print("COLETA:")
print("- Use a CollectorGun (azul) para coletar recursos")
print("- Segure o botão esquerdo do mouse em um recurso para encolhê-lo")
print("- Quando o recurso atingir tamanho mínimo, você o coletará")
print("- Carregando recursos deixa você mais lento")
print("")
print("COMBATE:")
print("- Use a CombatGun (preta) para atacar outros jogadores")
print("- Não é possível atacar membros da sua própria equipe")
print("- Morrer reduz o tamanho da sua base e faz você perder recursos")
print("")
print("DEPÓSITO:")
print("- Entre na barreira da sua base carregando recursos para depositá-los")
print("- Depositar recursos aumenta o tamanho da sua base")
print("- Recursos depositados restauram sua velocidade normal")
print("")
print("ATAQUE À BASE:")
print("- Use a CollectorGun para atacar barreiras e plataformas de bases inimigas")
print("- Ataques reduzem o tamanho da base inimiga lentamente")
print("- Se a barreira tocar a torre central, a base é destruída!")
print("- Bases destruídas ficam disponíveis para reivindicação novamente")
print("")
print("CONSTRUÇÃO:")
print("- Entre na sua barreira para acessar o menu de construção")
print("- Pressione 'B' para abrir/fechar o menu de construção")
print("- Recursos depositados se dividem: 60% para BaseSize, 40% para Materiais")
print("- Use materiais para construir muros, torres e outras defesas")
print("- Construções são destruídas se a base encolher demais")