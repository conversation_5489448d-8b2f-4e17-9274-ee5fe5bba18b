# Jogo de Arena e Sobrevivência - Roblox Studio

## Descrição
Este é um jogo de arena e sobrevivência multiplayer para Roblox Studio com sistema de bases reivindicáveis e formação de duplas. O jogo suporta até 12 bases simultâneas em um sistema "cada um por si" (Free-for-All).

## Fases Implementadas

### Fase 1: Fundação da Arena
- **Bases Reivindicáveis**: 12 bases espalhadas pelo mapa que podem ser reivindicadas por jogadores
- **Sistema de Duplas**: Jogadores podem formar equipes de até 2 pessoas
- **Cores de Equipe**: Cada base tem uma cor única que é aplicada aos jogadores
- **Interface Intuitiva**: Sistema de convites e informações da base

### Fase 2: O Loop de Ação Central
- **Sistema de Coleta**: Mecânica única de encolhimento de recursos
- **Combate PvP**: Sistema de armas com dano e respawn
- **Depósito de Recursos**: Entrega de recursos na base para crescimento
- **Ferramentas**: CombatGun e CollectorGun com mecânicas distintas

### Fase 3: A Base Viva e Dinâmica
- **Expansão Visual**: Bases crescem e encolhem visualmente baseado no tamanho
- **Efeitos da Barreira**: Cura aliados e causa dano a inimigos
- **Condição de Derrota**: Bases são destruídas quando a barreira toca a torre
- **Ataque à Base**: CollectorGun pode atacar bases inimigas diretamente

### Fase 4: Construção e Polimento
- **Sistema de Materiais**: Recursos se dividem entre BaseSize e BuildingMaterials
- **Construção Estratégica**: 4 tipos de defesas construíveis com materiais
- **Interface Completa**: HUD mostra vida, tamanho da base e materiais
- **Destruição por Encolhimento**: Construções são perdidas se a base encolher

### Estrutura do Projeto

```
src/
├── ServerScriptService/
│   ├── GameInitializer.lua      # Script principal de inicialização
│   ├── BaseManager.lua          # Gerencia as bases reivindicáveis
│   ├── InviteManager.lua        # Sistema de convites para duplas
│   ├── ResourceManager.lua      # Gerencia recursos coletáveis no mapa
│   ├── CollectionManager.lua    # Sistema de coleta de recursos
│   ├── CombatManager.lua        # Sistema de combate PvP
│   ├── DepositManager.lua       # Sistema de depósito de recursos
│   ├── BaseController.lua       # Módulo para controle visual das bases
│   ├── BaseAttackManager.lua    # Sistema de ataque às bases
│   └── BuildingManager.lua      # Sistema de construção de defesas
├── ServerStorage/
│   ├── CreateBaseTemplate.lua   # Cria o modelo de base
│   └── CreateTools.lua          # Cria as ferramentas (armas)
├── ReplicatedStorage/
│   ├── RemoteEvents.lua         # Eventos de comunicação cliente-servidor
│   └── GameConfig.lua           # Configurações centralizadas
├── StarterPack/
│   ├── CombatGun.lua           # Script da arma de combate
│   └── CollectorGun.lua        # Script da ferramenta de coleta
└── StarterGui/
    ├── InviteUI.lua            # Interface de convites
    ├── BaseInfoUI.lua          # Interface de informações da base
    ├── GameplayUI.lua          # Interface de gameplay (vida, recursos)
    ├── BuildingUI.lua          # Interface de construção
    └── MainHUD.lua             # HUD principal completo
```

### Como Usar

#### Configuração Inicial
1. Abra o projeto no Roblox Studio
2. Execute o script `GameInitializer.lua` em ServerScriptService
3. O sistema criará automaticamente:
   - BaseTemplate em ServerStorage
   - RemoteEvents em ReplicatedStorage
   - 12 bases posicionadas no mapa

#### Gameplay

**Bases:**
1. **Reivindicar Base**: Toque em um ClaimPad amarelo para reivindicar uma base
2. **Formar Dupla**: Use o botão "CONVIDAR" para enviar convites a outros jogadores
3. **Aceitar/Recusar Convites**: Responda aos convites recebidos através da interface

**Coleta de Recursos:**
1. **Equipar CollectorGun**: Ferramenta azul para coletar recursos
2. **Coletar**: Segure o botão esquerdo do mouse em um recurso para encolhê-lo
3. **Completar Coleta**: Quando o recurso atingir tamanho mínimo, você o coletará
4. **Lentidão**: Carregando recursos deixa você mais lento

**Combate:**
1. **Equipar CombatGun**: Ferramenta preta para combate
2. **Atacar**: Clique para disparar projéteis em outros jogadores
3. **Proteção de Equipe**: Não é possível atacar membros da sua própria equipe
4. **Consequências**: Morrer reduz o tamanho da sua base e faz você perder recursos

**Depósito:**
1. **Entrar na Base**: Entre na barreira da sua base carregando recursos
2. **Depósito Automático**: Recursos são automaticamente depositados
3. **Crescimento**: Depositar recursos aumenta o tamanho da sua base
4. **Restauração**: Velocidade normal é restaurada após o depósito

**Ataque à Base:**
1. **Usar CollectorGun**: A mesma ferramenta de coleta pode atacar bases
2. **Mirar na Barreira**: Segure o botão esquerdo na barreira ou plataforma inimiga
3. **Dano Contínuo**: Ataque reduz o tamanho da base inimiga lentamente
4. **Destruição**: Se a barreira tocar a torre central, a base é destruída
5. **Reconquista**: Bases destruídas ficam disponíveis para reivindicação

**Efeitos da Barreira:**
1. **Proteção Aliada**: Estar na sua barreira cura você lentamente
2. **Zona Perigosa**: Entrar na barreira inimiga causa dano contínuo
3. **Crescimento Visual**: Bases crescem e encolhem baseado no tamanho

**Construção:**
1. **Divisão de Recursos**: 60% vai para BaseSize, 40% para BuildingMaterials
2. **Menu de Construção**: Pressione 'B' dentro da sua barreira
3. **4 Tipos de Defesas**: Muros, Torres, Geradores e Depósitos
4. **Posicionamento**: Clique para posicionar após selecionar
5. **Destruição**: Construções são perdidas se a base encolher demais

**Interface (HUD):**
1. **Barra de Vida**: Mostra HP atual e máximo
2. **Barra da Base**: Mostra tamanho atual da base (0-500)
3. **Materiais**: Ícone e número de BuildingMaterials disponíveis
4. **Status**: Notificações de ações importantes

#### Componentes da Base
Cada base contém:
- **BasePlatform**: Plataforma cilíndrica de grama (chão da base)
- **CoreTower**: Torre central com bandeira
- **SpawnLocation**: Ponto de respawn (ativado após reivindicação)
- **Barrier**: Barreira esférica semi-transparente
- **ClaimPad**: Pad para reivindicar a base

### Configurações

As configurações podem ser ajustadas em `GameConfig.lua`:

```lua
-- Exemplo de configurações
BASE_CONFIG = {
    MAX_BASES = 12,
    BASE_SIZE_DEFAULT = 100,
    BARRIER_RADIUS = 20
}

TEAM_CONFIG = {
    MAX_PLAYERS_PER_BASE = 2,
    AVAILABLE_COLORS = {...}
}
```

### Sistema de Cores
- Cada base recebe uma cor única quando reivindicada
- A cor é aplicada à base e aos uniformes dos jogadores
- 12 cores diferentes disponíveis para as equipes

### Funcionalidades da Interface

#### InviteUI
- Botão "CONVIDAR" para abrir menu de jogadores
- Lista de jogadores disponíveis para convite
- Sistema de notificações para feedback

#### BaseInfoUI
- Mostra informações da base atual do jogador
- Exibe dono, parceiro e status da base
- Atualização em tempo real

#### Tipos de Recursos
O jogo inclui 4 tipos de recursos coletáveis:
- **Pedra** (Stone): Valor 10, mais comum
- **Metal**: Valor 15, comum
- **Cristal**: Valor 25, raro, brilhante
- **Madeira** (Wood): Valor 8, menos comum

#### Tipos de Construções
4 defesas construíveis com BuildingMaterials:
- **Muro de Pedra**: 25 materiais, defesa básica
- **Torre de Vigia**: 50 materiais, observação elevada
- **Gerador de Barreira**: 75 materiais, fortalece defesas
- **Depósito de Recursos**: 40 materiais, aumenta capacidade

### Próximas Fases (Planejadas)
- Expansão e defesa de bases
- Sistema de economia avançado
- Upgrades de base e tecnologias
- Eventos especiais e boss fights

### Requisitos Técnicos
- Roblox Studio
- Conhecimento básico de Luau/Lua
- Estrutura de projeto Rojo (opcional)

### Instalação
1. Clone ou baixe o projeto
2. Abra no Roblox Studio
3. Execute `GameInitializer.lua`
4. Teste no modo Play

### Suporte
Para dúvidas ou problemas, verifique:
1. Console de saída para mensagens de erro
2. Estrutura de arquivos está correta
3. Todos os scripts estão nos locais corretos

---

**Desenvolvido para Roblox Studio usando Luau**