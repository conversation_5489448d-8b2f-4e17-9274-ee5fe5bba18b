-- BuildingUI.lua
-- Interface para o sistema de construção de defesas

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")
local mouse = player:GetMouse()

-- Aguarda RemoteEvents
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local requestBuildEvent = remoteEventsFolder:WaitForChild("RequestBuild")
local buildResponseEvent = remoteEventsFolder:WaitForChild("BuildResponse")

local BuildingUI = {}

-- Configurações de construção (sincronizadas com o servidor)
local BUILDING_TYPES = {
    {
        id = "stone_wall",
        name = "Muro de Pedra",
        cost = 25,
        size = Vector3.new(8, 6, 2),
        color = BrickColor.new("Dark stone grey"),
        description = "Muro defensivo básico"
    },
    {
        id = "watch_tower",
        name = "Torre de Vigia",
        cost = 50,
        size = Vector3.new(4, 12, 4),
        color = BrickColor.new("Brown"),
        description = "Torre alta para observação"
    },
    {
        id = "barrier_generator",
        name = "Gerador de Barreira",
        cost = 75,
        size = Vector3.new(3, 3, 3),
        color = BrickColor.new("Cyan"),
        description = "Fortalece a barreira da base"
    },
    {
        id = "resource_storage",
        name = "Depósito de Recursos",
        cost = 40,
        size = Vector3.new(6, 4, 6),
        color = BrickColor.new("Bright yellow"),
        description = "Aumenta capacidade de materiais"
    }
}

-- Variáveis da UI
local buildingGui
local buildingFrame
local previewPart
local selectedBuildingType
local isInOwnBarrier = false

-- Função para verificar se está na própria barreira
local function checkIfInOwnBarrier()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    
    -- Procura a base do jogador
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            local barrier = base:FindFirstChild("Barrier")
            
            if barrier and owner and (owner.Value == player or (partner and partner.Value == player)) then
                local distance = (playerPosition - barrier.Position).Magnitude
                local barrierRadius = barrier.Size.X / 2
                
                if distance <= barrierRadius then
                    return true
                end
            end
        end
    end
    
    return false
end

-- Cria a interface de construção
local function createBuildingUI()
    -- ScreenGui principal
    buildingGui = Instance.new("ScreenGui")
    buildingGui.Name = "BuildingUI"
    buildingGui.ResetOnSpawn = false
    buildingGui.Parent = playerGui
    
    -- Frame principal
    buildingFrame = Instance.new("Frame")
    buildingFrame.Name = "BuildingFrame"
    buildingFrame.Size = UDim2.new(0, 400, 0, 500)
    buildingFrame.Position = UDim2.new(1, -420, 0.5, -250)
    buildingFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    buildingFrame.BorderSizePixel = 0
    buildingFrame.Visible = false
    buildingFrame.Parent = buildingGui
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "CONSTRUÇÃO"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = buildingFrame
    
    -- Botão fechar
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = buildingFrame
    
    -- Lista de construções
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "BuildingList"
    scrollFrame.Size = UDim2.new(1, -20, 1, -60)
    scrollFrame.Position = UDim2.new(0, 10, 0, 50)
    scrollFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = buildingFrame
    
    -- Cria botões para cada tipo de construção
    local yOffset = 0
    for _, buildingType in ipairs(BUILDING_TYPES) do
        local buildingButton = Instance.new("Frame")
        buildingButton.Name = buildingType.id
        buildingButton.Size = UDim2.new(1, -10, 0, 80)
        buildingButton.Position = UDim2.new(0, 5, 0, yOffset)
        buildingButton.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
        buildingButton.BorderSizePixel = 1
        buildingButton.BorderColor3 = Color3.new(0.3, 0.3, 0.3)
        buildingButton.Parent = scrollFrame
        
        local nameLabel = Instance.new("TextLabel")
        nameLabel.Size = UDim2.new(0.7, 0, 0.4, 0)
        nameLabel.Position = UDim2.new(0, 10, 0, 5)
        nameLabel.BackgroundTransparency = 1
        nameLabel.Text = buildingType.name
        nameLabel.TextColor3 = Color3.new(1, 1, 1)
        nameLabel.TextScaled = true
        nameLabel.Font = Enum.Font.SourceSansBold
        nameLabel.TextXAlignment = Enum.TextXAlignment.Left
        nameLabel.Parent = buildingButton
        
        local costLabel = Instance.new("TextLabel")
        costLabel.Size = UDim2.new(0.25, 0, 0.4, 0)
        costLabel.Position = UDim2.new(0.7, 0, 0, 5)
        costLabel.BackgroundTransparency = 1
        costLabel.Text = "Custo: " .. buildingType.cost
        costLabel.TextColor3 = Color3.new(1, 1, 0)
        costLabel.TextScaled = true
        costLabel.Font = Enum.Font.SourceSans
        costLabel.Parent = buildingButton
        
        local descLabel = Instance.new("TextLabel")
        descLabel.Size = UDim2.new(1, -20, 0.4, 0)
        descLabel.Position = UDim2.new(0, 10, 0.4, 0)
        descLabel.BackgroundTransparency = 1
        descLabel.Text = buildingType.description
        descLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
        descLabel.TextScaled = true
        descLabel.Font = Enum.Font.SourceSans
        descLabel.TextXAlignment = Enum.TextXAlignment.Left
        descLabel.Parent = buildingButton
        
        local buildButton = Instance.new("TextButton")
        buildButton.Size = UDim2.new(0.8, 0, 0.25, 0)
        buildButton.Position = UDim2.new(0.1, 0, 0.7, 0)
        buildButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.2)
        buildButton.BorderSizePixel = 0
        buildButton.Text = "CONSTRUIR"
        buildButton.TextColor3 = Color3.new(1, 1, 1)
        buildButton.TextScaled = true
        buildButton.Font = Enum.Font.SourceSansBold
        buildButton.Parent = buildingButton
        
        buildButton.MouseButton1Click:Connect(function()
            startBuildingPlacement(buildingType)
        end)
        
        yOffset = yOffset + 90
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset)
    
    closeButton.MouseButton1Click:Connect(function()
        buildingFrame.Visible = false
        clearPreview()
    end)
end

-- Função para iniciar o posicionamento de construção
function startBuildingPlacement(buildingType)
    selectedBuildingType = buildingType
    buildingFrame.Visible = false
    
    -- Cria preview da construção
    createPreview(buildingType)
    
    -- Conecta eventos do mouse
    local connection
    connection = mouse.Button1Down:Connect(function()
        if previewPart then
            local position = previewPart.Position
            requestBuildEvent:FireServer(buildingType.id, position)
            clearPreview()
            connection:Disconnect()
        end
    end)
    
    -- Conecta tecla ESC para cancelar
    local escConnection
    escConnection = UserInputService.InputBegan:Connect(function(input)
        if input.KeyCode == Enum.KeyCode.Escape then
            clearPreview()
            connection:Disconnect()
            escConnection:Disconnect()
        end
    end)
end

-- Cria preview da construção
function createPreview(buildingType)
    clearPreview()
    
    previewPart = Instance.new("Part")
    previewPart.Name = "BuildingPreview"
    previewPart.Size = buildingType.size
    previewPart.BrickColor = buildingType.color
    previewPart.Material = Enum.Material.ForceField
    previewPart.Transparency = 0.5
    previewPart.CanCollide = false
    previewPart.Anchored = true
    previewPart.Parent = workspace
    
    -- Atualiza posição do preview
    local updateConnection
    updateConnection = RunService.Heartbeat:Connect(function()
        if previewPart and previewPart.Parent then
            local hit = mouse.Hit
            if hit then
                previewPart.Position = hit.Position + Vector3.new(0, buildingType.size.Y / 2, 0)
            end
        else
            updateConnection:Disconnect()
        end
    end)
end

-- Remove preview
function clearPreview()
    if previewPart then
        previewPart:Destroy()
        previewPart = nil
    end
    selectedBuildingType = nil
end

-- Mostra notificação
local function showNotification(message, color)
    -- Reutiliza o sistema de notificação existente se disponível
    local gameplayUI = playerGui:FindFirstChild("GameplayUI")
    if gameplayUI then
        local notificationFrame = gameplayUI:FindFirstChild("NotificationFrame")
        if notificationFrame then
            local notificationText = notificationFrame:FindFirstChild("NotificationText")
            if notificationText then
                notificationText.Text = message
                notificationFrame.BackgroundColor3 = color or Color3.new(0.1, 0.1, 0.1)
                notificationFrame.Visible = true
                
                -- Anima
                notificationFrame.Position = UDim2.new(0.5, -200, 1, 0)
                local tweenIn = TweenService:Create(
                    notificationFrame,
                    TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
                    {Position = UDim2.new(0.5, -200, 0.8, 0)}
                )
                tweenIn:Play()
                
                spawn(function()
                    wait(3)
                    local tweenOut = TweenService:Create(
                        notificationFrame,
                        TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
                        {Position = UDim2.new(0.5, -200, 1, 0)}
                    )
                    tweenOut:Play()
                    tweenOut.Completed:Connect(function()
                        notificationFrame.Visible = false
                    end)
                end)
            end
        end
    end
end

-- Loop principal para verificar se está na barreira
local function updateBuildingUI()
    local wasInBarrier = isInOwnBarrier
    isInOwnBarrier = checkIfInOwnBarrier()
    
    if isInOwnBarrier and not wasInBarrier then
        -- Entrou na barreira
        buildingFrame.Visible = true
    elseif not isInOwnBarrier and wasInBarrier then
        -- Saiu da barreira
        buildingFrame.Visible = false
        clearPreview()
    end
end

-- Conecta eventos
buildResponseEvent.OnClientEvent:Connect(function(status, message)
    if status == "success" then
        showNotification(message, Color3.new(0.2, 0.8, 0.2))
    elseif status == "error" then
        showNotification(message, Color3.new(0.8, 0.2, 0.2))
    end
end)

-- Conecta tecla B para abrir/fechar construção
UserInputService.InputBegan:Connect(function(input)
    if input.KeyCode == Enum.KeyCode.B then
        if isInOwnBarrier then
            buildingFrame.Visible = not buildingFrame.Visible
            if not buildingFrame.Visible then
                clearPreview()
            end
        end
    end
end)

-- Loop de atualização
RunService.Heartbeat:Connect(updateBuildingUI)

-- Inicializa a UI
createBuildingUI()

print("BuildingUI inicializada com sucesso!")

return BuildingUI