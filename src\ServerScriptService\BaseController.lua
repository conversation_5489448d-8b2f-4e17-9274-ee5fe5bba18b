-- BaseController.lua
-- ModuleScript responsável por controlar o tamanho visual das bases

local BaseController = {}

-- Configurações de escala
local MIN_BASE_SIZE = 10
local MAX_BASE_SIZE = 500
local DEFAULT_BASE_SIZE = 100

-- Configurações dos componentes da base
local BASE_PLATFORM_CONFIG = {
    minSize = Vector3.new(2, 20, 20),
    maxSize = Vector3.new(2, 60, 60),
    defaultSize = Vector3.new(2, 30, 30)
}

local BARRIER_CONFIG = {
    minSize = Vector3.new(25, 6, 25),  -- Altura reduzida de 8 para 6
    maxSize = Vector3.new(60, 12, 60), -- Altura reduzida de 16 para 12
    defaultSize = Vector3.new(40, 8, 40) -- Altura reduzida de 12 para 8
}

local CORE_TOWER_CONFIG = {
    size = Vector3.new(2, 20, 2), -- <PERSON><PERSON>ho fixo
    position = Vector3.new(0, 11, 0) -- Posição relativa
}

-- Função para calcular escala baseada no BaseSize
local function calculateScale(baseSize)
    -- Normaliza o baseSize entre 0 e 1
    local normalizedSize = math.max(0, math.min(1, (baseSize - MIN_BASE_SIZE) / (MAX_BASE_SIZE - MIN_BASE_SIZE)))
    return normalizedSize
end

-- Função para interpolar entre dois Vector3
local function lerpVector3(a, b, t)
    return Vector3.new(
        a.X + (b.X - a.X) * t,
        a.Y + (b.Y - a.Y) * t,
        a.Z + (b.Z - a.Z) * t
    )
end

-- Função principal para atualizar o tamanho da base
function BaseController.UpdateBaseSize(baseModel)
    if not baseModel or not baseModel.Parent then
        return false
    end
    
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    if not baseSizeValue then
        warn("BaseSize não encontrado em " .. baseModel.Name)
        return false
    end
    
    local baseSize = baseSizeValue.Value
    local scale = calculateScale(baseSize)
    
    -- Atualiza BasePlatform
    local basePlatform = baseModel:FindFirstChild("BasePlatform")
    if basePlatform then
        local newSize = lerpVector3(BASE_PLATFORM_CONFIG.minSize, BASE_PLATFORM_CONFIG.maxSize, scale)
        basePlatform.Size = newSize
        
        -- Mantém a posição Y correta
        local currentPos = basePlatform.Position
        basePlatform.Position = Vector3.new(currentPos.X, 1, currentPos.Z)
    end
    
    -- Atualiza Barrier
    local barrier = baseModel:FindFirstChild("Barrier")
    if barrier then
        local newSize = lerpVector3(BARRIER_CONFIG.minSize, BARRIER_CONFIG.maxSize, scale)
        barrier.Size = newSize
        
        -- Mantém a posição Y correta (barreira tocando o chão)
        local currentPos = barrier.Position
        barrier.Position = Vector3.new(currentPos.X, newSize.Y / 2 + 0.5, currentPos.Z)
    end
    
    -- Verifica condição de derrota
    BaseController.CheckDefeatCondition(baseModel)
    
    print("Base " .. baseModel.Name .. " atualizada. Tamanho: " .. baseSize .. ", Escala: " .. scale)
    return true
end

-- Função para verificar condição de derrota
function BaseController.CheckDefeatCondition(baseModel)
    if not baseModel or not baseModel.Parent then
        return
    end
    
    local barrier = baseModel:FindFirstChild("Barrier")
    local coreTower = baseModel:FindFirstChild("CoreTower")
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    
    if not barrier or not coreTower or not baseSizeValue then
        return
    end
    
    -- Calcula se a barreira tocou a torre central
    local barrierRadius = barrier.Size.X / 2
    local towerPosition = coreTower.Position
    local barrierPosition = barrier.Position
    local distance = (towerPosition - barrierPosition).Magnitude
    
    -- Se a distância for menor que o raio da barreira, a base foi derrotada
    if distance <= barrierRadius or baseSizeValue.Value <= MIN_BASE_SIZE then
        BaseController.DestroyBase(baseModel)
    end
end

-- Função para destruir/resetar uma base
function BaseController.DestroyBase(baseModel)
    if not baseModel or not baseModel.Parent then
        return
    end
    
    print("Base " .. baseModel.Name .. " foi destruída!")
    
    -- Reseta valores
    local owner = baseModel:FindFirstChild("Owner")
    local partner = baseModel:FindFirstChild("Partner")
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    local buildingMaterialsValue = baseModel:FindFirstChild("BuildingMaterials")
    local spawnLocation = baseModel:FindFirstChild("SpawnLocation")
    
    -- Remove jogadores da base
    if owner and owner.Value then
        local ownerPlayer = owner.Value
        ownerPlayer.RespawnLocation = nil
        owner.Value = nil
    end
    
    if partner and partner.Value then
        local partnerPlayer = partner.Value
        partnerPlayer.RespawnLocation = nil
        partner.Value = nil
    end
    
    -- Reseta tamanho e materiais
    if baseSizeValue then
        baseSizeValue.Value = DEFAULT_BASE_SIZE
    end
    
    if buildingMaterialsValue then
        buildingMaterialsValue.Value = 0
    end
    
    -- Desativa spawn
    if spawnLocation then
        spawnLocation.Enabled = false
    end
    
    -- Reseta cores para cinza
    local basePlatform = baseModel:FindFirstChild("BasePlatform")
    local coreTower = baseModel:FindFirstChild("CoreTower")
    local barrier = baseModel:FindFirstChild("Barrier")
    
    if basePlatform then
        basePlatform.BrickColor = BrickColor.new("Medium stone grey")
    end
    
    if coreTower then
        coreTower.BrickColor = BrickColor.new("Dark stone grey")
    end
    
    if barrier then
        barrier.BrickColor = BrickColor.new("Light stone grey")
        barrier.Transparency = 0.8
    end
    
    -- Atualiza tamanho visual
    BaseController.UpdateBaseSize(baseModel)
    
    -- Reativa o ClaimPad
    local claimPad = baseModel:FindFirstChild("ClaimPad")
    if claimPad then
        claimPad.BrickColor = BrickColor.new("Bright yellow")
        
        -- Reativa o texto
        local surfaceGui = claimPad:FindFirstChild("SurfaceGui")
        if surfaceGui then
            local textLabel = surfaceGui:FindFirstChild("TextLabel")
            if textLabel then
                textLabel.Text = "CLAIM BASE"
            end
        end
    end
    
    -- Efeito visual de destruição
    BaseController.CreateDestructionEffect(baseModel)
end

-- Função para criar efeito visual de destruição
function BaseController.CreateDestructionEffect(baseModel)
    local coreTower = baseModel:FindFirstChild("CoreTower")
    if not coreTower then return end
    
    -- Cria explosão de partículas
    local attachment = Instance.new("Attachment")
    attachment.Parent = coreTower
    
    local explosion = Instance.new("ParticleEmitter")
    explosion.Texture = "rbxasset://textures/particles/fire_main.dds"
    explosion.Color = ColorSequence.new(Color3.new(1, 0.5, 0))
    explosion.Size = NumberSequence.new{
        NumberSequenceKeypoint.new(0, 2),
        NumberSequenceKeypoint.new(1, 0)
    }
    explosion.Lifetime = NumberRange.new(1, 3)
    explosion.Rate = 100
    explosion.SpreadAngle = Vector2.new(180, 180)
    explosion.Speed = NumberRange.new(10, 20)
    explosion.Parent = attachment
    
    -- Para a emissão após um tempo
    spawn(function()
        wait(2)
        explosion.Enabled = false
        wait(5)
        attachment:Destroy()
    end)
    
    -- Som de destruição
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/impact_water.mp3"
    sound.Volume = 0.8
    sound.Pitch = 0.5
    sound.Parent = coreTower
    sound:Play()
    
    game:GetService("Debris"):AddItem(sound, 5)
end

-- Função para obter informações da base
function BaseController.GetBaseInfo(baseModel)
    if not baseModel then return nil end
    
    local baseSizeValue = baseModel:FindFirstChild("BaseSize")
    local owner = baseModel:FindFirstChild("Owner")
    local partner = baseModel:FindFirstChild("Partner")
    local barrier = baseModel:FindFirstChild("Barrier")
    
    return {
        size = baseSizeValue and baseSizeValue.Value or 0,
        owner = owner and owner.Value or nil,
        partner = partner and partner.Value or nil,
        barrierRadius = barrier and barrier.Size.X / 2 or 0,
        position = baseModel.PrimaryPart and baseModel.PrimaryPart.Position or Vector3.new(0, 0, 0)
    }
end

-- Função para verificar se um jogador está dentro da barreira
function BaseController.IsPlayerInBarrier(player, baseModel)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local baseInfo = BaseController.GetBaseInfo(baseModel)
    if not baseInfo then return false end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    local distance = (playerPosition - baseInfo.position).Magnitude
    
    return distance <= baseInfo.barrierRadius
end

-- Função para verificar se um jogador é da equipe da base
function BaseController.IsPlayerTeammate(player, baseModel)
    local baseInfo = BaseController.GetBaseInfo(baseModel)
    if not baseInfo then return false end
    
    return baseInfo.owner == player or baseInfo.partner == player
end

return BaseController