-- ConfigureStarterPlayer.lua
-- Script para configurar o StarterPlayer adequadamente

local StarterPlayer = game:GetService("StarterPlayer")
local StarterPack = game:GetService("StarterPack")

local function configureStarterPlayer()
    -- Configurações do StarterPlayer (usando pcall para evitar erros)
    pcall(function()
        StarterPlayer.CharacterWalkSpeed = 16
        StarterPlayer.CharacterJumpPower = 50
        -- CharacterMaxHealth e RespawnTime não existem mais no StarterPlayer
        StarterPlayer.LoadCharacterAppearance = true
        StarterPlayer.UserEmotesEnabled = true
    end)
    
    -- Configurações de câmera
    StarterPlayer.CameraMaxZoomDistance = 400
    StarterPlayer.CameraMinZoomDistance = 0.5
    StarterPlayer.CameraMode = Enum.CameraMode.Classic
    
    -- Configurações de controle
    StarterPlayer.DevComputerMovementMode = Enum.DevComputerMovementMode.UserChoice
    StarterPlayer.DevTouchMovementMode = Enum.DevTouchMovementMode.UserChoice
    
    -- Remove ferramentas padrão do StarterPack se existirem
    for _, tool in ipairs(StarterPack:GetChildren()) do
        if tool:IsA("Tool") and (tool.Name == "ClassicSword" or tool.Name == "Slingshot") then
            tool:Destroy()
        end
    end
    
    print("StarterPlayer configurado com sucesso!")
    print("- Velocidade: 16")
    print("- Ferramentas padrão removidas")
end

-- Executa a configuração
configureStarterPlayer()

-- Retorna true para indicar sucesso
return true