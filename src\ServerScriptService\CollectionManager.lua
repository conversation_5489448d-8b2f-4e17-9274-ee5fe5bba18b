-- CollectionManager.lua
-- Script responsável por gerenciar a coleta de recursos no servidor

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
local startCollectingEvent = remoteEventsFolder:WaitForChild("StartCollecting")
local stopCollectingEvent = remoteEventsFolder:WaitForChild("StopCollecting")
local collectResourceEvent = remoteEventsFolder:WaitForChild("CollectResource")

local ResourceManager = require(script.Parent.ResourceManager)

local CollectionManager = {}

-- Configurações
local COLLECTION_SLOWDOWN = 0.3 -- Multiplica a velocidade por este valor
local NORMAL_WALKSPEED = 16

-- Função para aplicar lentidão ao jogador
local function applySlowdown(player)
    if player.Character and player.Character:Find<PERSON>irst<PERSON>hild("Humanoid") then
        local humanoid = player.Character.Humanoid
        humanoid.WalkSpeed = NORMAL_WALKSPEED * COLLECTION_SLOWDOWN
    end
end

-- Função para remover lentidão do jogador
local function removeSlowdown(player)
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        local humanoid = player.Character.Humanoid
        humanoid.WalkSpeed = NORMAL_WALKSPEED
    end
end

-- Função para definir estado de carregamento de recurso
local function setCarryingResource(player, carrying, resourceValue)
    if not player.Character then return end
    
    local carryingValue = player.Character:FindFirstChild("CarregandoRecurso")
    if not carryingValue then
        carryingValue = Instance.new("BoolValue")
        carryingValue.Name = "CarregandoRecurso"
        carryingValue.Parent = player.Character
    end
    
    carryingValue.Value = carrying
    
    if carrying then
        -- Adiciona valor do recurso
        local resourceValueObj = player.Character:FindFirstChild("ResourceValue")
        if not resourceValueObj then
            resourceValueObj = Instance.new("NumberValue")
            resourceValueObj.Name = "ResourceValue"
            resourceValueObj.Parent = player.Character
        end
        resourceValueObj.Value = resourceValue or 0
        
        -- Aplica lentidão
        applySlowdown(player)
        
        -- Efeito visual (partículas ou brilho)
        local character = player.Character
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if humanoidRootPart then
            local attachment = Instance.new("Attachment")
            attachment.Name = "ResourceCarryingEffect"
            attachment.Parent = humanoidRootPart
            
            local particles = Instance.new("ParticleEmitter")
            particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
            particles.Color = ColorSequence.new(Color3.new(1, 1, 0))
            particles.Size = NumberSequence.new(0.5)
            particles.Lifetime = NumberRange.new(1, 2)
            particles.Rate = 20
            particles.SpreadAngle = Vector2.new(45, 45)
            particles.Speed = NumberRange.new(2, 4)
            particles.Parent = attachment
        end
    else
        -- Remove valor do recurso
        local resourceValueObj = player.Character:FindFirstChild("ResourceValue")
        if resourceValueObj then
            resourceValueObj:Destroy()
        end
        
        -- Remove lentidão
        removeSlowdown(player)
        
        -- Remove efeito visual
        local character = player.Character
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if humanoidRootPart then
            local effect = humanoidRootPart:FindFirstChild("ResourceCarryingEffect")
            if effect then
                effect:Destroy()
            end
        end
    end
end

-- Manipula início da coleta
startCollectingEvent.OnServerEvent:Connect(function(player, resource)
    if not resource or not resource.Parent then return end
    
    -- Verifica se o jogador já está carregando um recurso
    if player.Character and player.Character:FindFirstChild("CarregandoRecurso") then
        local carrying = player.Character.CarregandoRecurso.Value
        if carrying then
            return -- Já está carregando um recurso
        end
    end
    
    -- Verifica se o recurso tem os componentes necessários
    if not resource:FindFirstChild("OriginalSize") or not resource:FindFirstChild("ResourceType") then
        return
    end
    
    -- Verifica distância
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        local distance = (player.Character.HumanoidRootPart.Position - resource.Position).Magnitude
        if distance > 100 then -- Máximo 100 studs
            return
        end
    end
    
    -- Inicia a coleta
    local success = ResourceManager.startCollecting(resource, player)
    if success then
        print(player.Name .. " iniciou coleta de " .. resource.Name)
    end
end)

-- Manipula parada da coleta
stopCollectingEvent.OnServerEvent:Connect(function(player, resource)
    if not resource then return end
    
    ResourceManager.stopCollecting(resource)
    print(player.Name .. " parou coleta de " .. resource.Name)
end)

-- Manipula coleta completa (chamado pelo ResourceManager)
collectResourceEvent.OnClientEvent:Connect(function(player, resourceValue)
    setCarryingResource(player, true, resourceValue)
    print(player.Name .. " coletou recurso no valor de " .. resourceValue)
end)

-- Conecta ao evento de coleta completa do ResourceManager
local originalCompleteCollection = ResourceManager.completeCollection
ResourceManager.completeCollection = function(resource, collector)
    local resourceValue = resource:FindFirstChild("ResourceValue")
    local value = resourceValue and resourceValue.Value or 10
    
    -- Chama a função original
    originalCompleteCollection(resource, collector)
    
    -- Define o estado de carregamento
    setCarryingResource(collector, true, value)
end

-- Limpa estado quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    -- Para qualquer coleta em andamento
    for _, resource in ipairs(workspace:GetChildren()) do
        if resource:FindFirstChild("ResourceType") then
            local collector = ResourceManager.getCollector(resource)
            if collector == player then
                ResourceManager.stopCollecting(resource)
            end
        end
    end
end)

-- Restaura velocidade quando jogador spawna
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function(character)
        wait(1) -- Aguarda o character carregar completamente
        removeSlowdown(player)
    end)
end)

print("CollectionManager inicializado com sucesso!")

return CollectionManager